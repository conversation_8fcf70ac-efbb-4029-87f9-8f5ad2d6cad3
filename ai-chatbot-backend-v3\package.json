{"name": "chatbot-v3", "version": "1.0.0", "description": "Improved chatbot with better structure for database queries", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "test": "node test-providers.js"}, "dependencies": {"@langchain/core": "^0.3.61", "@langchain/google-genai": "^0.2.13", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "langchain": "^0.3.29", "node-fetch": "^3.3.2", "pg": "^8.16.2", "typeorm": "^0.3.25", "zod": "^3.25.67"}, "keywords": ["chatbot", "gemini", "database", "providers"], "author": "", "license": "ISC"}