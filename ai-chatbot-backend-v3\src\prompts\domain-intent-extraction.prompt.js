import { PromptTemplate } from "@langchain/core/prompts";

export const domainIntentExtractionPrompt = PromptTemplate.fromTemplate(`
{message}

JSON: {{"d":"general|provider|service|service_request","i":"greeting|provider_listing|specific_provider|provider_filter|help|service_request","q":"query","e":{{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general|detailed|filtered|unknown","f":null}}

Guidelines:
- If the user provides car details, appointment, address, provider, or instructions as part of a service booking or request, classify the domain as "service_request" and the intent as "service_request".
- **CRITICAL: If the context shows the user is in the middle of a service booking flow, ALWAYS classify as "service_request" domain, regardless of the user's message content.**
- Look for context clues like: asking for car details, date/time, address, provider selection, special instructions, confirmation, or booking.
- Context keywords that indicate service request flow: "service request", "booking", "appointment", "car details", "date and time", "address", "provider", "special instructions", "confirm", "book", "schedule".
- If the context mentions any service request related information (car, appointment, address, provider assignment, etc.), treat ALL subsequent messages as "service_request" domain.
- Messages like "no", "yes", "please book", "assign", or any response during a service flow should be classified as "service_request".
- **When a provider needs to be assigned, ALWAYS ask the user to select a provider. DO NOT auto-assign a provider unless the user explicitly says 'choose any', 'any provider', or similar. Only then, assign any available provider.**

Examples:
"hi" → {{"d":"general","i":"greeting","q":"hi","e":{{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":null}}
"list providers" → {{"d":"provider","i":"provider_listing","q":"list providers","e":{{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":null}}
"5 providers" → {{"d":"provider","i":"provider_listing","q":"5 providers","e":{{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":{{"limit":5}}}}
"providers in NY" → {{"d":"provider","i":"provider_filter","q":"providers in NY","e":{{"p":null,"s":null,"l":"NY","d":null,"t":null,"pr":null,"o":null}},"qt":"filtered","f":{{"location":"NY"}}}}
"tell me about Ajay" → {{"d":"provider","i":"specific_provider","q":"Ajay","e":{{"p":"Ajay","s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"detailed","f":null}}
"I need an oil change" → {{"d":"service","i":"service_request","q":"I need an oil change","e":{{"p":null,"s":"oil change","l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":{{"service":"oil change"}}}}
"create a service request" → {{"d":"service_request","i":"service_request","q":"create a service request","e":{{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":null}}
"Chevrolet Pilot 2020 Limited, 3.5L V6, CVT" → {{"d":"service_request","i":"service_request","q":"Chevrolet Pilot 2020 Limited, 3.5L V6, CVT","e":{{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":null}}

Context-aware examples:
Context: "Do you have any special instructions for the provider?"
User: "no" → {{"d":"service_request","i":"service_request","q":"no","e":{{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":null}}

Context: "Would you like me to assign this Ajay Koli to your service request?"
User: "yes Assign Ajay Koli" → {{"d":"service_request","i":"service_request","q":"yes Assign Ajay Koli","e":{{"p":"Ajay Koli","s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":null}}

Context: "Please select a provider for your service request."
User: "choose any" → {{"d":"service_request","i":"service_request","q":"choose any","e":{{"p":"any","s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":{{"provider":"any"}}}}

Context: "Please select a provider for your service request."
User: "Ajay Koli" → {{"d":"service_request","i":"service_request","q":"Ajay Koli","e":{{"p":"Ajay Koli","s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":{{"provider":"Ajay Koli"}}}}

Context: "Ajay has been assigned to your service request. Do you have any special instructions?"
User: "please book my service request" → {{"d":"service_request","i":"service_request","q":"please book my service request","e":{{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":null}}

Context: "Do you have any special instructions for the provider?"
User: "no no special instructions" → {{"d":"service_request","i":"service_request","q":"no no special instructions","e":{{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null}},"qt":"general","f":null}}

JSON only.`);
