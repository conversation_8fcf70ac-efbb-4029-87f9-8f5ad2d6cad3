# AI Chatbot Frontend

This is a React-based chat UI with speech-to-text (STT) and text-to-speech (TTS) features using free browser APIs. It connects to your backend for conversational AI.

## Features

- Chat UI (user/AI messages)
- AI responses are spoken aloud (TTS)
- User can speak messages (STT, English only)
- Free, no external speech services needed

## Getting Started

1. **Install dependencies:**

   ```bash
   cd ai-chatbot-frontend
   npm install
   # or
   pnpm install
   ```

2. **Start the development server:**

   ```bash
   npm start
   # or
   pnpm start
   ```

3. **Open in browser:**
   Visit [http://localhost:5173](http://localhost:5173) (default Vite port).

## Notes

- The chat UI expects a backend endpoint at `/api/conversation` that accepts `{ message: string }` and returns `{ reply: string }`.
- TTS and STT use browser APIs (works best in Chrome/Edge).
- Only English is supported for speech features.
