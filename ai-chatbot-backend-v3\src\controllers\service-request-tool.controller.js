import { ServiceRequestTool } from "../tools/service-request.tool.js";

export class ServiceRequestToolController {
  /**
   * Create a service request using the dedicated tool
   * POST /api/service-request-tool/create
   */
  static async createServiceRequest(req, res) {
    try {
      console.log(`[INFO] ServiceRequestTool API: Create service request`);
      console.log(`[DEBUG] Request body:`, JSON.stringify(req.body, null, 2));

      const { userId, vehicle, location, appointment, provider, instructions } = req.body;

      // Validate required fields
      if (!userId) {
        return res.status(400).json({
          success: false,
          error: "userId is required"
        });
      }

      if (!vehicle || !location) {
        return res.status(400).json({
          success: false,
          error: "vehicle and location are required"
        });
      }

      const requestData = {
        vehicle,
        location,
        appointment: appointment || {},
        provider: provider || {},
        instructions: instructions || {}
      };

      const result = await ServiceRequestTool.createServiceRequest(requestData, userId);

      if (result.success) {
        res.status(201).json({
          success: true,
          message: "Service request created successfully",
          data: {
            serviceRequest: result.serviceRequest,
            vehicle: result.vehicle,
            address: result.address,
            provider: result.provider,
            createdEntities: result.createdEntities
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: "Failed to create service request",
          validationIssues: result.validationIssues
        });
      }
    } catch (error) {
      console.error(`[ERROR] ServiceRequestTool API: Create failed:`, error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
        message: error.message
      });
    }
  }

  /**
   * Get user's vehicles
   * GET /api/service-request-tool/vehicles/:userId
   */
  static async getUserVehicles(req, res) {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: "userId is required"
        });
      }

      const vehicles = await ServiceRequestTool.getUserVehicles(parseInt(userId));

      res.status(200).json({
        success: true,
        data: vehicles
      });
    } catch (error) {
      console.error(`[ERROR] ServiceRequestTool API: Get vehicles failed:`, error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
        message: error.message
      });
    }
  }

  /**
   * Get user's addresses
   * GET /api/service-request-tool/addresses/:userId
   */
  static async getUserAddresses(req, res) {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: "userId is required"
        });
      }

      const addresses = await ServiceRequestTool.getUserAddresses(parseInt(userId));

      res.status(200).json({
        success: true,
        data: addresses
      });
    } catch (error) {
      console.error(`[ERROR] ServiceRequestTool API: Get addresses failed:`, error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
        message: error.message
      });
    }
  }

  /**
   * Find similar vehicles
   * POST /api/service-request-tool/vehicles/similar
   */
  static async findSimilarVehicles(req, res) {
    try {
      const { userId, vehicle } = req.body;

      if (!userId || !vehicle) {
        return res.status(400).json({
          success: false,
          error: "userId and vehicle data are required"
        });
      }

      const similarVehicles = await ServiceRequestTool.findSimilarVehicles(vehicle, userId);

      res.status(200).json({
        success: true,
        data: similarVehicles
      });
    } catch (error) {
      console.error(`[ERROR] ServiceRequestTool API: Find similar vehicles failed:`, error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
        message: error.message
      });
    }
  }

  /**
   * Find similar addresses
   * POST /api/service-request-tool/addresses/similar
   */
  static async findSimilarAddresses(req, res) {
    try {
      const { userId, address } = req.body;

      if (!userId || !address) {
        return res.status(400).json({
          success: false,
          error: "userId and address data are required"
        });
      }

      const similarAddresses = await ServiceRequestTool.findSimilarAddresses(address, userId);

      res.status(200).json({
        success: true,
        data: similarAddresses
      });
    } catch (error) {
      console.error(`[ERROR] ServiceRequestTool API: Find similar addresses failed:`, error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
        message: error.message
      });
    }
  }

  /**
   * Validate service request data
   * POST /api/service-request-tool/validate
   */
  static async validateServiceRequestData(req, res) {
    try {
      const { userId, vehicle, location } = req.body;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: "userId is required"
        });
      }

      const validationResult = {
        isValid: true,
        issues: []
      };

      // Validate vehicle if provided
      if (vehicle) {
        const vehicleResult = await ServiceRequestTool.handleVehicle(vehicle, userId);
        if (vehicleResult.issues.length > 0) {
          validationResult.isValid = false;
          validationResult.issues.push(...vehicleResult.issues);
        }
      }

      // Validate address if provided
      if (location) {
        const addressResult = await ServiceRequestTool.handleAddress(location, userId);
        if (addressResult.issues.length > 0) {
          validationResult.isValid = false;
          validationResult.issues.push(...addressResult.issues);
        }
      }

      res.status(200).json({
        success: true,
        data: validationResult
      });
    } catch (error) {
      console.error(`[ERROR] ServiceRequestTool API: Validation failed:`, error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
        message: error.message
      });
    }
  }
}
