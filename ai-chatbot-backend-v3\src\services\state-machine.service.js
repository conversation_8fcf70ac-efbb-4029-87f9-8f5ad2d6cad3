/**
 * State Machine Service for Service Request Flow
 * Replaces complex LLM-based flow with deterministic state management
 */

import { tokenCounter } from "../utils/token-counter.js";

// Service Request States
export const STATES = {
  INIT: "init",
  VEHICLE: "vehicle",
  ADDRESS: "address",
  DATETIME: "datetime",
  PROVIDER: "provider",
  CONFIRM: "confirm",
  COMPLETE: "complete",
};

// Regex patterns for data extraction
const PATTERNS = {
  // Vehicle patterns: "2020 Honda Civic", "Tesla Model 3 2021", etc.
  vehicle:
    /(?:(\d{4})\s+)?([a-zA-Z]+)\s+((?:Model\s+)?[a-zA-Z0-9\s]+?)(?:\s+(\d{4}))?$/i,

  // Address patterns
  address:
    /(\d+.*?(?:street|st|avenue|ave|road|rd|drive|dr|lane|ln|way|blvd|boulevard|circle|ct|court|place|pl).*?)(?:,\s*([^,]+))(?:,\s*([A-Z]{2}))(?:,?\s*(\d{5}))?/i,

  // Date/time patterns
  datetime:
    /(tomorrow|today|monday|tuesday|wednesday|thursday|friday|saturday|sunday|\d{1,2}\/\d{1,2}|\d{1,2}-\d{1,2})(?:\s+(?:at\s+)?(\d{1,2}(?::\d{2})?\s*(?:am|pm|AM|PM)?))?/i,

  // Confirmation patterns
  confirmation:
    /^(yes|yeah|yep|sure|ok|okay|confirm|book|proceed|go ahead|please book|book it)$/i,

  // Provider patterns
  provider: /(auto|automatic|any|don't care|doesn't matter)/i,
};

class StateMachineService {
  constructor() {
    this.sessions = new Map(); // Store session states
  }

  // Get current session state
  getSessionState(sessionId) {
    if (!this.sessions.has(sessionId)) {
      this.sessions.set(sessionId, {
        state: STATES.INIT,
        data: {},
        created: Date.now(),
      });
    }
    return this.sessions.get(sessionId);
  }

  // Update session state
  updateSessionState(sessionId, newState, data = {}) {
    const session = this.getSessionState(sessionId);
    session.state = newState;
    session.data = { ...session.data, ...data };
    session.updated = Date.now();

    console.log(
      `[STATE] ${sessionId}: ${newState} | Data: ${JSON.stringify(data)}`
    );
    return session;
  }

  // Helper to check if message looks like vehicle info
  looksLikeVehicle(message) {
    const vehicleKeywords =
      /\b(honda|toyota|ford|bmw|audi|mercedes|tesla|civic|camry|f150|model|year|\d{4})\b/i;
    return vehicleKeywords.test(message);
  }

  // Extract data using regex patterns
  extractData(message, currentState) {
    const extracted = {};
    const msg = message.trim();

    // Vehicle extraction - only extract if message looks like vehicle info
    if (
      currentState === STATES.VEHICLE ||
      (currentState === STATES.INIT && this.looksLikeVehicle(msg))
    ) {
      const vehicleMatch = msg.match(PATTERNS.vehicle);
      if (vehicleMatch) {
        const [, year1, make, model, year2] = vehicleMatch;
        extracted.vehicle = {
          year: parseInt(year1 || year2) || null,
          make: make?.trim(),
          model: model?.trim(),
        };
      }
    }

    // Address extraction
    if (currentState === STATES.ADDRESS || currentState === STATES.INIT) {
      const addressMatch = msg.match(PATTERNS.address);
      if (addressMatch) {
        const [, street, city, state, zip] = addressMatch;
        extracted.address = {
          street: street?.trim(),
          city: city?.trim(),
          state: state?.trim(),
          zip: zip?.trim(),
        };
      }
    }

    // DateTime extraction
    if (currentState === STATES.DATETIME || currentState === STATES.INIT) {
      const datetimeMatch = msg.match(PATTERNS.datetime);
      if (datetimeMatch) {
        const [, date, time] = datetimeMatch;
        extracted.datetime = {
          date: date?.trim(),
          time: time?.trim() || null,
        };
      }
    }

    // Confirmation extraction
    const confirmMatch = msg.match(PATTERNS.confirmation);
    if (confirmMatch) {
      extracted.confirmation = true;
    }

    // Provider extraction
    if (currentState === STATES.PROVIDER) {
      const providerMatch = msg.match(PATTERNS.provider);
      if (providerMatch) {
        extracted.provider = { preference: "auto" };
      }
    }

    console.log(`[EXTRACT] "${msg}" -> ${JSON.stringify(extracted)}`);
    return extracted;
  }

  // Determine next state based on current state and extracted data
  getNextState(currentState, extractedData, sessionData) {
    // Handle confirmations first
    if (extractedData.confirmation) {
      if (currentState === STATES.CONFIRM) {
        return STATES.COMPLETE;
      }
      // Confirmation in other states means "yes, add this data"
      return this.getNextMissingField(sessionData);
    }

    // State transitions based on extracted data
    switch (currentState) {
      case STATES.INIT:
        if (extractedData.vehicle) {
          return STATES.ADDRESS;
        }
        return STATES.VEHICLE;

      case STATES.VEHICLE:
        if (extractedData.vehicle) {
          return STATES.ADDRESS;
        }
        return STATES.VEHICLE; // Stay in vehicle state

      case STATES.ADDRESS:
        if (extractedData.address) {
          return STATES.DATETIME;
        }
        return STATES.ADDRESS; // Stay in address state

      case STATES.DATETIME:
        if (extractedData.datetime) {
          return STATES.PROVIDER;
        }
        return STATES.DATETIME; // Stay in datetime state

      case STATES.PROVIDER:
        if (extractedData.provider) {
          return STATES.CONFIRM;
        }
        return STATES.PROVIDER; // Stay in provider state

      case STATES.CONFIRM:
        return STATES.CONFIRM; // Stay until confirmation

      case STATES.COMPLETE:
        return STATES.INIT; // Reset for new request

      default:
        return STATES.VEHICLE;
    }
  }

  // Find next missing field
  getNextMissingField(sessionData) {
    if (!sessionData.vehicle) return STATES.VEHICLE;
    if (!sessionData.address) return STATES.ADDRESS;
    if (!sessionData.datetime) return STATES.DATETIME;
    if (!sessionData.provider) return STATES.PROVIDER;
    return STATES.CONFIRM;
  }

  // Generate response message based on state
  generateResponse(state, extractedData, sessionData) {
    const responses = {
      [STATES.INIT]:
        "I'll help you book a service. What's your vehicle year, make, and model?",
      [STATES.VEHICLE]:
        "What's your vehicle year, make, and model? (e.g., '2020 Honda Civic')",
      [STATES.ADDRESS]:
        "Where should the service take place? Please provide the address.",
      [STATES.DATETIME]:
        "When would you like to schedule the service? (e.g., 'Tomorrow at 2 PM')",
      [STATES.PROVIDER]:
        "Do you have a preferred provider, or should I auto-assign one? (say 'auto' for auto-assign)",
      [STATES.CONFIRM]: this.generateConfirmationMessage(sessionData),
      [STATES.COMPLETE]:
        "✅ Service request created successfully! You'll receive a confirmation shortly.",
    };

    return responses[state] || responses[STATES.VEHICLE];
  }

  // Generate confirmation message with summary
  generateConfirmationMessage(sessionData) {
    const { vehicle, address, datetime, provider } = sessionData;

    return `Perfect! Here's your service request summary:

🚗 **Vehicle:** ${vehicle?.year || ""} ${vehicle?.make || ""} ${
      vehicle?.model || ""
    }
📍 **Address:** ${address?.street || ""}, ${address?.city || ""}, ${
      address?.state || ""
    } ${address?.zip || ""}
📅 **Date/Time:** ${datetime?.date || ""} ${
      datetime?.time ? `at ${datetime.time}` : ""
    }
👤 **Provider:** ${
      provider?.preference === "auto" ? "Auto-assigned" : "Specific provider"
    }

Reply 'yes' to confirm and book this service.`;
  }

  // Check if all required data is collected
  isDataComplete(sessionData) {
    return !!(
      sessionData.vehicle &&
      sessionData.address &&
      sessionData.datetime &&
      sessionData.provider
    );
  }

  // Process message and return response
  async processMessage(message, sessionId, userId) {
    const startTime = Date.now();

    // Get current session
    const session = this.getSessionState(sessionId);
    const currentState = session.state;

    // Extract data using regex (fast!)
    const extractedData = this.extractData(message, currentState);

    // Update session data
    const updatedData = { ...session.data, ...extractedData };

    // Determine next state
    const nextState = this.getNextState(
      currentState,
      extractedData,
      updatedData
    );

    // Update session
    this.updateSessionState(sessionId, nextState, extractedData);

    // Generate response
    const responseMessage = this.generateResponse(
      nextState,
      extractedData,
      updatedData
    );

    // Log performance (no LLM calls = 0 tokens!)
    const duration = Date.now() - startTime;
    console.log(
      `[PERFORMANCE] State machine processed in ${duration}ms - 0 tokens used! 🚀`
    );

    return {
      message: responseMessage,
      data: {
        state: nextState,
        collected: updatedData,
        isComplete: this.isDataComplete(updatedData),
      },
    };
  }

  // Reset session
  resetSession(sessionId) {
    this.sessions.delete(sessionId);
    console.log(`[STATE] Reset session: ${sessionId}`);
  }

  // Clean up old sessions (call periodically)
  cleanupOldSessions(maxAgeMs = 30 * 60 * 1000) {
    // 30 minutes
    const now = Date.now();
    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - (session.updated || session.created) > maxAgeMs) {
        this.sessions.delete(sessionId);
        console.log(`[CLEANUP] Removed old session: ${sessionId}`);
      }
    }
  }
}

// Export singleton instance
export const stateMachineService = new StateMachineService();

// Cleanup old sessions every 10 minutes
setInterval(() => {
  stateMachineService.cleanupOldSessions();
}, 10 * 60 * 1000);
