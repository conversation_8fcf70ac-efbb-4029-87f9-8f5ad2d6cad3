import { LLMService } from "../services/llm.service.js";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { createToolCallingAgent, AgentExecutor } from "langchain/agents";
import agentPrompt from "../prompts/agent.prompt.js";
import { intentTool } from "./intent.tool.js";
import { providerTool } from "./provider.tool.js";

// LangChain-compatible tool array with handlers
const tools = [
  {
    name: "intentRouter",
    description:
      "Routes user messages to the correct domain service based on detected domain.",
    parameters: {
      type: "object",
      properties: {
        userMessage: { type: "string", description: "The user's message." },
        userId: {
          type: "string",
          description: "Optional user ID for context.",
        },
      },
      required: ["userMessage"],
    },
    func: async (args) => intentTool.call(args),
  },
  {
    name: "providerInfo",
    description: "Get information about a provider by name.",
    parameters: {
      type: "object",
      properties: {
        providerName: {
          type: "string",
          description: "The name of the provider to look up.",
        },
      },
      required: ["providerName"],
    },
    func: async (args) => providerTool.call(args),
  },
];

const prompt = ChatPromptTemplate.fromMessages([
  ["system", agentPrompt.template],
  ["placeholder", "{chat_history}"],
  ["human", "{input}"],
  ["placeholder", "{agent_scratchpad}"],
]);

const agent = createToolCallingAgent({
  llm: LLMService.llm,
  tools,
  prompt,
});

const agentExecutor = new AgentExecutor({
  agent,
  tools,
});

export async function runAgent(input) {
  const result = await agentExecutor.invoke({ input });
  return result;
}
