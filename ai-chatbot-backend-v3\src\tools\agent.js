import { intentTool } from "./intent.tool.js";
import { providerTool } from "./provider.tool.js";
import { serviceRequestTool } from "./service-request.tool.js";
import { LLMService } from "../services/llm.service.js";
import * as chatMemory from "../services/chat-memory.service.js";
import {
  toolSelectionPrompt,
  providerExtractionPrompt,
} from "../prompts/tool-selection.prompt.js";

// LLM-based tool selection - let Gemini Flash 2.0 decide which tool to use
export async function runAgent(input) {
  try {
    console.log(`[INFO] LLM Agent - Processing input: ${input}`);

    // Extract userId and sessionId from input
    const userIdMatch = input.match(/User ID: (\d+)/);
    const sessionIdMatch = input.match(/Session ID: (\d+)/);
    const messageMatch = input.match(/Message: (.+)$/);

    const userId = userIdMatch ? parseInt(userIdMatch[1]) : 4;
    const sessionId = sessionIdMatch ? parseInt(sessionIdMatch[1]) : 101;
    const message = messageMatch ? messageMatch[1] : input;

    console.log(
      `[DEBUG] Extracted - userId: ${userId}, sessionId: ${sessionId}, message: ${message}`
    );

    // Get recent conversation history for context-aware tool selection
    const historyMessages = await chatMemory.getHistory(sessionId);
    const recentHistory = historyMessages
      .slice(-4) // Get last 4 messages for context
      .map((msg) => `${msg.role || "User"}: ${msg.content}`)
      .join("\n");

    console.log(`[DEBUG] Recent history for tool selection: ${recentHistory}`);

    // Use LLM to determine which tool to use with conversation context
    const toolSelectionPromptFormatted = await toolSelectionPrompt.format({
      message,
      history: recentHistory || "No previous conversation",
    });
    const selectedTool = await LLMService.generateResponse(
      toolSelectionPromptFormatted
    );
    const toolName = selectedTool.trim().toLowerCase();

    console.log(`[INFO] LLM selected tool: ${toolName}`);

    let result;

    // Use switch case for better organization
    switch (true) {
      case toolName.includes("servicerequest"):
        console.log(`[INFO] Using serviceRequestTool`);
        result = await serviceRequestTool.call({
          message,
          userId,
          sessionId,
          action: "create",
        });
        break;

      case toolName.includes("provider"):
        console.log(`[INFO] Using providerTool`);
        // Use LLM to extract provider name
        const providerPromptFormatted = await providerExtractionPrompt.format({
          message,
        });
        const providerName = await LLMService.generateResponse(
          providerPromptFormatted
        );
        result = await providerTool.call({ providerName: providerName.trim() });
        break;

      default:
        console.log(`[INFO] Using intentTool (default)`);
        result = await intentTool.call({
          message,
          userId,
          sessionId,
        });
        break;
    }

    console.log(`[INFO] LLM Agent - Result:`, JSON.stringify(result, null, 2));

    // Properly structure response with message and data separation
    const response = {
      message:
        result.message ||
        result.response ||
        "I'm sorry, I couldn't process your request.",
    };

    // Add data object if there's additional data
    if (result.data && typeof result.data === "object") {
      response.data = result.data;
    }

    return { output: response };
  } catch (error) {
    console.error(`[ERROR] LLM Agent failed:`, error);
    return {
      output:
        "I'm sorry, I encountered an error while processing your request. Please try again.",
    };
  }
}

// Removed LangChain agent code - using simple pattern matching approach instead
