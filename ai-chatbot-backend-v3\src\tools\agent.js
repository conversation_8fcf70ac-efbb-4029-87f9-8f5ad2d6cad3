import { LLMService } from "../services/llm.service.js";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { createToolCallingAgent, AgentExecutor } from "langchain/agents";
import agentPrompt from "../prompts/agent.prompt.js";
import { intentTool } from "./intent.tool.js";
import { providerTool } from "./provider.tool.js";
import { serviceRequestTool } from "./service-request.tool.js";

// LangChain-compatible tool array with handlers
const tools = [
  {
    name: "intentRouter",
    description:
      "Routes user messages to the correct domain service based on detected domain. Use for general conversation and routing.",
    parameters: {
      type: "object",
      properties: {
        message: { type: "string", description: "The user's message." },
        userId: {
          type: "string",
          description: "User ID for context.",
        },
        sessionId: {
          type: "string",
          description: "Session ID for chat memory.",
        },
      },
      required: ["message", "userId", "sessionId"],
    },
    func: async (args) => intentTool.call(args),
  },
  {
    name: "providerInfo",
    description: "Get information about a provider by name.",
    parameters: {
      type: "object",
      properties: {
        providerName: {
          type: "string",
          description: "The name of the provider to look up.",
        },
      },
      required: ["providerName"],
    },
    func: async (args) => providerTool.call(args),
  },
  {
    name: "createServiceRequest",
    description:
      "Create a service request for vehicle maintenance. Use when user wants to book/schedule a service, has vehicle details, or wants to create an appointment.",
    parameters: {
      type: "object",
      properties: {
        message: {
          type: "string",
          description:
            "The user's message containing vehicle details, location, or service request information.",
        },
        userId: {
          type: "string",
          description: "User ID for the service request.",
        },
        sessionId: {
          type: "string",
          description: "Session ID for chat memory.",
        },
        action: {
          type: "string",
          description:
            "Action to perform: 'create', 'validate', 'list_vehicles', 'list_addresses'",
          enum: ["create", "validate", "list_vehicles", "list_addresses"],
          default: "create",
        },
      },
      required: ["message", "userId", "sessionId"],
    },
    func: async (args) => serviceRequestTool.call(args),
  },
];

const prompt = ChatPromptTemplate.fromMessages([
  ["system", agentPrompt.template],
  ["placeholder", "{chat_history}"],
  ["human", "{input}"],
  ["placeholder", "{agent_scratchpad}"],
]);

const agent = createToolCallingAgent({
  llm: LLMService.llm,
  tools,
  prompt,
});

const agentExecutor = new AgentExecutor({
  agent,
  tools,
});

export async function runAgent(input) {
  const result = await agentExecutor.invoke({ input });
  return result;
}
