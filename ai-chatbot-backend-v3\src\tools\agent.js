import { intentTool } from "./intent.tool.js";
import { providerTool } from "./provider.tool.js";
import { serviceRequestTool } from "./service-request.tool.js";
import { LLMService } from "../services/llm.service.js";

// LLM-based tool selection - let Gemini Flash 2.0 decide which tool to use
export async function runAgent(input) {
  try {
    console.log(`[INFO] LLM Agent - Processing input: ${input}`);

    // Extract userId and sessionId from input
    const userIdMatch = input.match(/User ID: (\d+)/);
    const sessionIdMatch = input.match(/Session ID: (\d+)/);
    const messageMatch = input.match(/Message: (.+)$/);

    const userId = userIdMatch ? parseInt(userIdMatch[1]) : 4;
    const sessionId = sessionIdMatch ? parseInt(sessionIdMatch[1]) : 101;
    const message = messageMatch ? messageMatch[1] : input;

    console.log(
      `[DEBUG] Extracted - userId: ${userId}, sessionId: ${sessionId}, message: ${message}`
    );

    // Use LLM to determine which tool to use
    const toolSelectionPrompt = `
You are a tool selection assistant. Based on the user's message, determine which tool should be used to handle their request.

Available tools:
1. "serviceRequestTool" - Use for vehicle service requests, maintenance, repairs, booking appointments, scheduling services, or when user mentions vehicle details (make, model, year, etc.)
2. "providerTool" - Use when user asks about specific providers, technicians, or service provider information
3. "intentTool" - Use for general conversation, questions about services, or anything else not covered by the above tools

User message: "${message}"

Respond with ONLY the tool name (serviceRequestTool, providerTool, or intentTool) and nothing else.
`;

    const selectedTool = await LLMService.generateResponse(toolSelectionPrompt);
    const toolName = selectedTool.trim().toLowerCase();

    console.log(`[INFO] LLM selected tool: ${toolName}`);

    let result;

    if (toolName.includes("servicerequest")) {
      console.log(`[INFO] Using serviceRequestTool`);
      result = await serviceRequestTool.call({
        message,
        userId,
        sessionId,
        action: "create",
      });
    } else if (toolName.includes("provider")) {
      console.log(`[INFO] Using providerTool`);
      // Use LLM to extract provider name
      const providerExtractionPrompt = `
Extract the provider name from this message: "${message}"
If no specific provider name is mentioned, respond with "unknown".
Respond with ONLY the provider name and nothing else.
`;
      const providerName = await LLMService.generateResponse(
        providerExtractionPrompt
      );
      result = await providerTool.call({ providerName: providerName.trim() });
    } else {
      console.log(`[INFO] Using intentTool`);
      result = await intentTool.call({
        message,
        userId,
        sessionId,
      });
    }

    console.log(`[INFO] LLM Agent - Result:`, JSON.stringify(result, null, 2));

    return {
      output:
        result.message ||
        result.response ||
        "I'm sorry, I couldn't process your request.",
    };
  } catch (error) {
    console.error(`[ERROR] LLM Agent failed:`, error);
    return {
      output:
        "I'm sorry, I encountered an error while processing your request. Please try again.",
    };
  }
}

// Removed LangChain agent code - using simple pattern matching approach instead
