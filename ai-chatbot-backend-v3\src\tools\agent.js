import { intentTool } from "./intent.tool.js";
import { providerTool } from "./provider.tool.js";
import { serviceRequestTool } from "./service-request.tool.js";

// For now, let's simplify and not use Lang<PERSON>hain agent with tools
// Instead, let's use a simple approach that works with Gemini Flash 2.0

export async function runAgent(input) {
  try {
    console.log(`[INFO] Simple Agent - Processing input: ${input}`);

    // Extract userId and sessionId from input
    const userIdMatch = input.match(/User ID: (\d+)/);
    const sessionIdMatch = input.match(/Session ID: (\d+)/);
    const messageMatch = input.match(/Message: (.+)$/);

    const userId = userIdMatch ? parseInt(userIdMatch[1]) : 4;
    const sessionId = sessionIdMatch ? parseInt(sessionIdMatch[1]) : 101;
    const message = messageMatch ? messageMatch[1] : input;

    console.log(
      `[DEBUG] Extracted - userId: ${userId}, sessionId: ${sessionId}, message: ${message}`
    );

    // Simple intent detection for tool selection
    const lowerMessage = message.toLowerCase();

    // Check for service request patterns
    const servicePatterns = [
      /brake.*service/i,
      /oil.*change/i,
      /maintenance/i,
      /repair/i,
      /service.*request/i,
      /book.*service/i,
      /schedule.*service/i,
      /appointment/i,
      /\d{4}.*honda|toyota|ford|chevrolet|bmw|mercedes|audi/i, // Year + car brand
      /honda|toyota|ford|chevrolet|bmw|mercedes|audi.*\d{4}/i, // Car brand + year
    ];

    const isServiceRequest = servicePatterns.some((pattern) =>
      pattern.test(message)
    );

    // Check for provider patterns
    const providerPatterns = [
      /tell.*about.*provider/i,
      /provider.*info/i,
      /who.*is.*provider/i,
    ];

    const isProviderQuery = providerPatterns.some((pattern) =>
      pattern.test(message)
    );

    let result;

    if (isServiceRequest) {
      console.log(`[INFO] Detected service request intent`);
      result = await serviceRequestTool.call({
        message,
        userId,
        sessionId,
        action: "create",
      });
    } else if (isProviderQuery) {
      console.log(`[INFO] Detected provider query intent`);
      // Extract provider name from message
      const providerNameMatch = message.match(/provider\s+([a-zA-Z\s]+)/i);
      const providerName = providerNameMatch
        ? providerNameMatch[1].trim()
        : "unknown";
      result = await providerTool.call({ providerName });
    } else {
      console.log(`[INFO] Using general intent router`);
      result = await intentTool.call({
        message,
        userId,
        sessionId,
      });
    }

    console.log(
      `[INFO] Simple Agent - Result:`,
      JSON.stringify(result, null, 2)
    );

    return {
      output:
        result.message ||
        result.response ||
        "I'm sorry, I couldn't process your request.",
    };
  } catch (error) {
    console.error(`[ERROR] Simple Agent failed:`, error);
    return {
      output:
        "I'm sorry, I encountered an error while processing your request. Please try again.",
    };
  }
}

// Removed LangChain agent code - using simple pattern matching approach instead
