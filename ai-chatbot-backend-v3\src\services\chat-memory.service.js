import { ConversationSummaryBufferMemory } from "langchain/memory";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";

const memoryMap = new Map();

function getOrCreateMemory(sessionId) {
  let memory = memoryMap.get(sessionId);
  if (!memory) {
    memory = new ConversationSummaryBufferMemory({
      llm: new ChatGoogleGenerativeAI({
        model: process.env.GEMINI_MODEL || "gemini-2.0-flash",
        apiKey: process.env.GOOGLE_API_KEY,
        temperature: 0.1,
      }),
      memoryKey: "history",
      returnMessages: true,
      maxTokenLimit: 1000,
    });
    memoryMap.set(sessionId, memory);
  }
  return memory;
}

export const getHistory = async (sessionId) => {
  const memory = getOrCreateMemory(sessionId);
  const { history } = await memory.loadMemoryVariables({});
  return history || [];
};

export const appendToHistory = async (sessionId, role, content) => {
  const memory = getOrCreateMemory(sessionId);

  if (role === "USER") {
    // For user messages, save immediately as input
    await memory.chatHistory.addUserMessage(content);
  } else {
    // For assistant messages, save as AI message
    await memory.chatHistory.addAIMessage(content);
  }
};

export const clearHistory = (sessionId) => {
  memoryMap.delete(sessionId);
};

export const getFormattedHistory = async (sessionId) => {
  const memory = getOrCreateMemory(sessionId);
  const { history } = await memory.loadMemoryVariables({});

  return (history || [])
    .map((msg) => {
      // Check message type using LangChain's _getType method
      const isHuman = msg._getType && msg._getType() === "human";
      const content = msg.content || "";
      return `${isHuman ? "User" : "Revi"}: ${content}`;
    })
    .join("\n");
};
