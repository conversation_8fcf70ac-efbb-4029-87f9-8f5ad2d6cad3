import React from "react";
import { useChat } from "./src/hooks/useChat.js";
import ChatHeader from "./src/components/ChatHeader.jsx";
import MessageList from "./src/components/MessageList.jsx";
import ChatInput from "./src/components/ChatInput.jsx";
import SpeechControls from "./src/components/SpeechControls.jsx";
import styles from "./src/styles/ChatApp.module.css";

// Read token and sessionId from environment variables
const TOKEN = import.meta.env.VITE_API_TOKEN || "";
const SESSION_ID = import.meta.env.VITE_SESSION_ID || "";

const ChatApp = () => {
  const {
    messages,
    input,
    setInput,
    isLoading,
    listening,
    isSpeaking,
    isPaused,
    micPermissionDenied,
    messagesEndRef,
    sendMessage,
    startListening,
    stopListening,
    pauseSpeech,
    resumeSpeech,
    stopSpeech,
  } = useChat(TOKEN, SESSION_ID);

  // Helper to check if only the welcome message is present
  const onlyWelcome =
    messages.length === 1 && messages[0].sender === "ai" && messages[0].text;

  return (
    <div className={styles.appWrapper}>
      <div className={styles.chatContainer}>
        <ChatHeader />
        <MessageList messages={messages} messagesEndRef={messagesEndRef} />
        {/* Show welcome message if only welcome is present and no user/AI chat yet */}
        {onlyWelcome && (
          <div
            style={{
              textAlign: "center",
              color: "#888",
              margin: "32px 0",
              fontSize: 18,
            }}
          >
            {messages[0].text}
          </div>
        )}
        <div className={styles.bottomPanel}>
          <SpeechControls
            isSpeaking={isSpeaking}
            isPaused={isPaused}
            onPauseSpeech={pauseSpeech}
            onResumeSpeech={resumeSpeech}
            onStopSpeech={stopSpeech}
          />
          <ChatInput
            input={input}
            setInput={setInput}
            onSendMessage={(msg) => sendMessage(msg, TOKEN, SESSION_ID)}
            listening={listening}
            onStartListening={startListening}
            onStopListening={stopListening}
            isLoading={isLoading}
            micPermissionDenied={micPermissionDenied}
          />
        </div>
      </div>
    </div>
  );
};

export default ChatApp;
