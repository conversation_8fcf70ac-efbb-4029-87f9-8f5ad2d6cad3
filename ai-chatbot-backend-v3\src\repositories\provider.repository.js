import { databaseService } from "../db/connection.js";
import { isValidProvider } from "../utils/provider.utils.js";
import { QueryBuilder } from "../utils/query-builder.utils.js";

function getRequiredTablesFromFilters(filters) {
  const requiredTables = new Set(["User"]);
  if (filters.location) requiredTables.add("ProviderLocation");
  if (filters.serviceCount || filters.services) {
    requiredTables.add("ProvidedService");
    requiredTables.add("ProvidedServiceType");
    requiredTables.add("ServiceType");
  }
  if (filters.priceRange) requiredTables.add("ProvidedServiceType");
  if (filters.rating) requiredTables.add("ProviderRating");
  if (filters.availability) requiredTables.add("ProviderStatus");
  if (filters.experience) requiredTables.add("ProviderProfile");
  if (filters.availableDay) requiredTables.add("ProvidedServiceAvailability");
  if (filters.slotStartTime || filters.slotEndTime) {
    requiredTables.add("ProvidedServiceAvailability");
    requiredTables.add("ProvidedServiceAvailabilitySlot");
  }
  if (filters.cancellationFee)
    requiredTables.add("ProvidedServiceCancellationPolicy");
  // ...add more as needed
  return Array.from(requiredTables);
}

export class ProviderRepository {
  static async findAvailableProviders(limit = 5) {
    try {
      const query = `
        SELECT 
          u.id,
          u."firstName",
          u."lastName", 
          u.email,
          u."mobileNumber"
        FROM "User" u
        WHERE u."role" = 'PROVIDER' 
          AND u."status" = 'active'
          AND u."firstName" IS NOT NULL
          AND u."lastName" IS NOT NULL
          AND u."firstName" != ''
          AND u."lastName" != ''
        ORDER BY u."firstName"
        LIMIT $1;
      `;

      const result = await databaseService
        .getDataSource()
        .query(query, [limit]);

      console.log("[SUCCESS] Found providers:", { count: result.length });

      // Format the results to match expected structure
      return result.map((row) => ({
        id: row.id,
        name: `${row.firstName.trim()} ${row.lastName.trim()}`,
        email: row.email,
        phone: row.mobileNumber,
        isActive: true,
        createdAt: new Date().toISOString(),
      }));
    } catch (error) {
      console.error("[ERROR] Error fetching available providers:", error);
      throw new Error("Failed to fetch available providers");
    }
  }

  static async findProviderByName(providerName, limit = 1) {
    try {
      // Create multiple search patterns for fuzzy matching
      const searchPatterns = [];

      // 1. Exact match (case insensitive)
      searchPatterns.push(
        `LOWER(CONCAT(u."firstName", ' ', u."lastName")) = LOWER('${providerName}')`
      );

      // 2. LIKE patterns for partial matches
      searchPatterns.push(
        `LOWER(CONCAT(u."firstName", ' ', u."lastName")) LIKE LOWER('%${providerName}%')`
      );
      searchPatterns.push(
        `LOWER(u."firstName") LIKE LOWER('%${providerName}%')`
      );
      searchPatterns.push(
        `LOWER(u."lastName") LIKE LOWER('%${providerName}%')`
      );

      // 3. Individual word matches (for typos and partial matches)
      const words = providerName.toLowerCase().split(/\s+/);
      words.forEach((word) => {
        if (word.length > 2) {
          // Only search for words longer than 2 characters
          searchPatterns.push(`LOWER(u."firstName") LIKE LOWER('%${word}%')`);
          searchPatterns.push(`LOWER(u."lastName") LIKE LOWER('%${word}%')`);
        }
      });

      // 4. Regex pattern for flexible matching (treats full name as single unit)
      const regexPattern = providerName.toLowerCase().replace(/\s+/g, "\\s*");
      searchPatterns.push(
        `LOWER(CONCAT(u."firstName", ' ', u."lastName")) ~ LOWER('${regexPattern}')`
      );

      const query = `
        SELECT 
          u.id,
          u."firstName",
          u."lastName",
          u.email,
          u."mobileNumber",
          st."name" as "serviceName",
          st."description",
          pst."minPrice",
          pst."maxPrice",
          CASE 
            WHEN LOWER(CONCAT(u."firstName", ' ', u."lastName")) = LOWER('${providerName}') THEN 100
            WHEN LOWER(CONCAT(u."firstName", ' ', u."lastName")) LIKE LOWER('%${providerName}%') THEN 90
            WHEN LOWER(u."firstName") LIKE LOWER('%${providerName}%') OR LOWER(u."lastName") LIKE LOWER('%${providerName}%') THEN 80
            WHEN LOWER(CONCAT(u."firstName", ' ', u."lastName")) ~ LOWER('${regexPattern}') THEN 70
            ELSE 50
          END as match_score
        FROM "User" u
        LEFT JOIN "ProvidedService" ps ON u.id = ps."userId"
        LEFT JOIN "ProvidedServiceType" pst ON ps.id = pst."providedServiceId"
        LEFT JOIN "ServiceType" st ON pst."serviceTypeId" = st.id
        WHERE u."role" = 'PROVIDER' 
          AND u."status" = 'active'
          AND u."firstName" IS NOT NULL
          AND u."lastName" IS NOT NULL
          AND u."firstName" != ''
          AND u."lastName" != ''
          AND u.email IS NOT NULL
          AND u.email != ''
          AND (${searchPatterns.join(" OR ")})
        ORDER BY match_score DESC, u."firstName", st."name"
        LIMIT $1;
      `;

      const result = await databaseService
        .getDataSource()
        .query(query, [limit * 3]);

      console.log(`[SUCCESS] Found providers matching "${providerName}":`, {
        count: result.length,
      });

      // Group by provider (same as chatbot-v2)
      const providersMap = new Map();

      result.forEach((row) => {
        if (!providersMap.has(row.id)) {
          const isValid = isValidProvider(row);

          console.log(
            `[INFO] Provider ${row.id}: firstName="${row.firstName}", lastName="${row.lastName}", email="${row.email}", valid=${isValid}, match_score=${row.match_score}`
          );

          if (isValid) {
            providersMap.set(row.id, {
              id: row.id,
              name: `${row.firstName.trim()} ${row.lastName.trim()}`,
              email: row.email.trim(),
              phone: row.mobileNumber,
              isActive: true,
              createdAt: new Date().toISOString(),
              services: [],
              matchScore: row.match_score,
            });
          }
        }

        if (row.serviceName) {
          const provider = providersMap.get(row.id);
          if (provider) {
            provider.services.push({
              name: row.serviceName,
              description: row.description,
              price:
                row.minPrice && row.maxPrice
                  ? `${row.minPrice} - ${row.maxPrice}`
                  : row.minPrice || row.maxPrice || "Price not specified",
            });
          }
        }
      });

      const finalProviders = Array.from(providersMap.values())
        .sort((a, b) => (b.matchScore || 0) - (a.matchScore || 0))
        .slice(0, limit);

      console.log(`[SUCCESS] Final providers with services:`, {
        count: finalProviders.length,
      });

      return finalProviders;
    } catch (error) {
      console.error("[ERROR] Error fetching provider by name:", error);
      throw new Error("Failed to fetch provider by name");
    }
  }

  static async findProviderById(id) {
    try {
      const query = `
        SELECT 
          u.id,
          u."firstName",
          u."lastName",
          u.email,
          u."mobileNumber"
        FROM "User" u
        WHERE u.id = $1 
          AND u."role" = 'PROVIDER'
          AND u."status" = 'active';
      `;

      const result = await databaseService.getDataSource().query(query, [id]);

      if (result.length === 0) {
        return null;
      }

      const row = result[0];
      return {
        id: row.id,
        name: `${row.firstName.trim()} ${row.lastName.trim()}`,
        email: row.email,
        phone: row.mobileNumber,
        isActive: true,
        createdAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error("[ERROR] Error fetching provider by ID:", error);
      throw new Error("Failed to fetch provider by ID");
    }
  }

  static async findProvidersWithServices(limit = 10) {
    try {
      const query = `
        SELECT 
          u.id,
          u."firstName",
          u."lastName",
          u.email,
          u."mobileNumber",
          st."name" as "serviceName",
          st."description",
          pst."minPrice",
          pst."maxPrice"
        FROM "User" u
        LEFT JOIN "ProvidedService" ps ON u.id = ps."userId"
        LEFT JOIN "ProvidedServiceType" pst ON ps.id = pst."providedServiceId"
        LEFT JOIN "ServiceType" st ON pst."serviceTypeId" = st.id
        WHERE u."role" = 'PROVIDER' 
          AND u."status" = 'active'
          AND u."firstName" IS NOT NULL
          AND u."lastName" IS NOT NULL
          AND u."firstName" != ''
          AND u."lastName" != ''
          AND u.email IS NOT NULL
          AND u.email != ''
        ORDER BY u."firstName", st."name"
        LIMIT $1;
      `;

      const result = await databaseService
        .getDataSource()
        .query(query, [limit * 3]); // Get more to group properly

      // Group by provider
      const providersMap = new Map();

      result.forEach((row) => {
        if (!providersMap.has(row.id)) {
          providersMap.set(row.id, {
            id: row.id,
            name: `${row.firstName.trim()} ${row.lastName.trim()}`,
            email: row.email.trim(),
            phone: row.mobileNumber,
            isActive: true,
            createdAt: new Date().toISOString(),
            services: [],
          });
        }

        if (row.serviceName) {
          const provider = providersMap.get(row.id);
          if (provider) {
            provider.services.push({
              name: row.serviceName,
              description: row.description,
              price:
                row.minPrice && row.maxPrice
                  ? `${row.minPrice} - ${row.maxPrice}`
                  : row.minPrice || row.maxPrice || "Price not specified",
            });
          }
        }
      });

      const finalProviders = Array.from(providersMap.values()).slice(0, limit);
      console.log("[SUCCESS] Final providers with services:", {
        count: finalProviders.length,
      });

      return finalProviders;
    } catch (error) {
      console.error("[ERROR] Error fetching providers with services:", error);
      throw new Error("Failed to fetch providers with services");
    }
  }

  static async findProvidersWithFilters(filters = {}, limit = 5) {
    try {
      // Map minServices to serviceCount filter for query builder (do this first!)
      if (filters.minServices !== undefined) {
        filters.serviceCount = {
          operator: "greater_than",
          value: filters.minServices,
        };
        delete filters.minServices;
      }
      // Map serviceType to services array for query builder
      if (filters.serviceType) {
        filters.services = [filters.serviceType];
        delete filters.serviceType;
      }
      // Now determine requiredTables automatically
      const requiredTables = getRequiredTablesFromFilters(filters);
      console.log("[INFO] Finding providers with filters:", {
        filters,
        requiredTables,
      });

      // Use the dynamic QueryBuilder to construct the query
      const { query, params } = QueryBuilder.buildProviderQuery(
        filters,
        requiredTables,
        limit
      );

      const result = await databaseService.getDataSource().query(query, params);

      console.log(`[SUCCESS] Found providers with filters:`, {
        count: result.length,
      });

      // Group by provider and handle dynamic fields
      const providersMap = new Map();

      result.forEach((row) => {
        if (!providersMap.has(row.id)) {
          const isValid = isValidProvider(row);

          if (isValid) {
            const provider = {
              id: row.id,
              name: `${row.firstName.trim()} ${row.lastName.trim()}`,
              email: row.email.trim(),
              phone: row.mobileNumber,
              isActive: true,
              createdAt: new Date().toISOString(),
              services: [],
              serviceCount: row.serviceCount || 0,
            };

            // Add dynamic fields based on available data
            if (row.location) provider.location = row.location;
            if (row.address) provider.address = row.address;
            if (row.providerRating) provider.rating = row.providerRating;
            if (row.reviewCount) provider.reviewCount = row.reviewCount;
            if (row.availability) provider.availability = row.availability;
            if (row.lastSeen) provider.lastSeen = row.lastSeen;
            if (row.experience) provider.experience = row.experience;
            if (row.specialization)
              provider.specialization = row.specialization;

            // Add availability fields
            if (row.availableDay) provider.availableDay = row.availableDay;
            if (row.slotStartTime) provider.slotStartTime = row.slotStartTime;
            if (row.slotEndTime) provider.slotEndTime = row.slotEndTime;

            // Add cancellation policy fields
            if (row.cancellationFee !== null)
              provider.cancellationFee = row.cancellationFee;

            providersMap.set(row.id, provider);
          }
        }

        if (row.serviceName) {
          const provider = providersMap.get(row.id);
          if (provider) {
            // Check if service already exists to avoid duplicates
            const serviceExists = provider.services.some(
              (s) => s.name === row.serviceName
            );
            if (!serviceExists) {
              provider.services.push({
                name: row.serviceName,
                description: row.description,
                price:
                  row.minPrice && row.maxPrice
                    ? `${row.minPrice} - ${row.maxPrice}`
                    : row.minPrice || row.maxPrice || "Price not specified",
              });
            }
          }
        }
      });

      const finalProviders = Array.from(providersMap.values())
        .sort((a, b) => (b.serviceCount || 0) - (a.serviceCount || 0))
        .slice(0, limit);

      console.log(`[SUCCESS] Final filtered providers:`, {
        count: finalProviders.length,
      });

      return finalProviders;
    } catch (error) {
      console.error("[ERROR] Error fetching providers with filters:", error);
      throw new Error("Failed to fetch providers with filters");
    }
  }
}
