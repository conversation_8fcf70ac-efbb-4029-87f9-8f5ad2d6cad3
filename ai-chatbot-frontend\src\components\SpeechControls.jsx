import React from "react";
import styles from "../styles/ChatApp.module.css";

const SpeechControls = ({
  isSpeaking,
  isPaused,
  onPauseSpeech,
  onResumeSpeech,
  onStopSpeech,
}) => {
  if (!isSpeaking && !isPaused) {
    return null;
  }

  return (
    <div className={styles.speechControlsCard}>
      <div className={styles.speechInfo}>
        <div className={styles.speechIcon}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path
              d="M3 11h3l3-3v8l-3-3H3V11zM13.5 12C13.5 10.23 12.77 8.68 11.5 7.68V16.32C12.77 15.32 13.5 13.77 13.5 12ZM16 12C16 14.91 14.39 17.39 12 18.18V20.9C15.5 20.07 18 16.91 18 12S15.5 3.93 12 3.1V5.82C14.39 6.61 16 9.09 16 12Z"
              fill="currentColor"
            />
          </svg>
        </div>
        <div className={styles.speechText}>
          <span className={styles.speechLabel}>🔊 Audio Playback</span>
          <span className={styles.speechStatus}>
            {isPaused ? "Paused" : "Playing response..."}
          </span>
        </div>
      </div>

      <div className={styles.speechActions}>
        <button
          onClick={isPaused ? onResumeSpeech : onPauseSpeech}
          className={styles.speechButton}
          title={isPaused ? "Resume" : "Pause"}
        >
          {isPaused ? (
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
              <polygon points="5,3 19,12 5,21" fill="currentColor" />
            </svg>
          ) : (
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
              <rect x="6" y="4" width="4" height="16" fill="currentColor" />
              <rect x="14" y="4" width="4" height="16" fill="currentColor" />
            </svg>
          )}
        </button>

        <button
          onClick={onStopSpeech}
          className={styles.speechButton}
          title="Stop"
        >
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
            <rect x="6" y="6" width="12" height="12" fill="currentColor" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default SpeechControls;
