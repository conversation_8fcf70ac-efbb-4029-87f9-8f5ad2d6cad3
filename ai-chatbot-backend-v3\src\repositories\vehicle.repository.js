import { databaseService } from "../db/connection.js";

export class VehicleRepository {
  /**
   * Check if a vehicle exists in the database for a specific user
   * @param {Object} vehicleData - Vehicle data to check
   * @param {number} userId - User ID
   * @returns {Object} - { exists: boolean, vehicle: Object|null }
   */
  static async checkVehicleExists(vehicleData, userId) {
    try {
      console.log(`[INFO] Checking if vehicle exists for user ${userId}:`, vehicleData);
      
      const query = `
        SELECT * FROM "Vehicle"
        WHERE "userId" = $1
          AND "industry" = $2
          AND "model" = $3
          AND "year" = $4
          AND "vehicleTrim" = $5
          AND "engine" = $6
          AND "driveTrain" = $7
          AND "status" != 'deleted'
        LIMIT 1;
      `;
      
      const values = [
        userId,
        vehicleData?.make || null,
        vehicleData?.model || null,
        vehicleData?.year || null,
        vehicleData?.trim || null,
        vehicleData?.engine || null,
        vehicleData?.transmission || null,
      ];

      const result = await databaseService.getDataSource().query(query, values);
      
      const exists = result.length > 0;
      console.log(`[INFO] Vehicle exists check result: ${exists}`);
      
      return {
        exists,
        vehicle: exists ? result[0] : null
      };
    } catch (error) {
      console.error(`[ERROR] Error checking vehicle existence:`, error);
      throw new Error("Failed to check vehicle existence");
    }
  }

  /**
   * Check if a similar vehicle exists (fuzzy matching)
   * @param {Object} vehicleData - Vehicle data to check
   * @param {number} userId - User ID
   * @returns {Array} - Array of similar vehicles
   */
  static async findSimilarVehicles(vehicleData, userId) {
    try {
      console.log(`[INFO] Finding similar vehicles for user ${userId}:`, vehicleData);
      
      const query = `
        SELECT * FROM "Vehicle"
        WHERE "userId" = $1
          AND "industry" ILIKE $2
          AND "model" ILIKE $3
          AND "status" != 'deleted'
        ORDER BY "year" DESC
        LIMIT 5;
      `;
      
      const values = [
        userId,
        `%${vehicleData?.make || ''}%`,
        `%${vehicleData?.model || ''}%`
      ];

      const result = await databaseService.getDataSource().query(query, values);
      
      console.log(`[INFO] Found ${result.length} similar vehicles`);
      
      return result;
    } catch (error) {
      console.error(`[ERROR] Error finding similar vehicles:`, error);
      throw new Error("Failed to find similar vehicles");
    }
  }

  /**
   * Create a new vehicle in the database
   * @param {Object} vehicleData - Vehicle data to create
   * @param {number} userId - User ID
   * @returns {Object} - Created vehicle
   */
  static async createVehicle(vehicleData, userId) {
    try {
      console.log(`[INFO] Creating new vehicle for user ${userId}:`, vehicleData);
      
      const query = `
        INSERT INTO "Vehicle" (
          "userId", "industry", "model", "year", "vehicleTrim", "engine", "driveTrain", "status", "createdAt", "updatedAt"
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'active', NOW(), NOW()) RETURNING *;
      `;
      
      const values = [
        userId,
        vehicleData?.make || null,
        vehicleData?.model || null,
        vehicleData?.year || null,
        vehicleData?.trim || null,
        vehicleData?.engine || null,
        vehicleData?.transmission || null,
      ];

      const result = await databaseService.getDataSource().query(query, values);
      
      console.log(`[SUCCESS] Created new vehicle:`, result[0]);
      
      return result[0];
    } catch (error) {
      console.error(`[ERROR] Error creating vehicle:`, error);
      throw new Error("Failed to create vehicle");
    }
  }

  /**
   * Get all vehicles for a user
   * @param {number} userId - User ID
   * @returns {Array} - Array of user's vehicles
   */
  static async getUserVehicles(userId) {
    try {
      console.log(`[INFO] Getting all vehicles for user ${userId}`);
      
      const query = `
        SELECT * FROM "Vehicle"
        WHERE "userId" = $1
          AND "status" != 'deleted'
        ORDER BY "createdAt" DESC;
      `;

      const result = await databaseService.getDataSource().query(query, [userId]);
      
      console.log(`[INFO] Found ${result.length} vehicles for user ${userId}`);
      
      return result;
    } catch (error) {
      console.error(`[ERROR] Error getting user vehicles:`, error);
      throw new Error("Failed to get user vehicles");
    }
  }
}
