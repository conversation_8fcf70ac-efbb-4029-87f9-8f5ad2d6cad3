export const serviceRequestLLMFlowPrompt = `
Collect: car details, date/time, address, provider preference, instructions.

Collected: {collected}
History: {history}

Ask for missing details one by one:
1. Vehicle (year, make, model)
2. Address (where service will be performed)
3. Date and time
4. Provider preference (ask: "Do you have a preferred provider, or would you like me to auto-assign one?")
5. Instructions (optional)

When all collected, show summary and ask to confirm.
`;
