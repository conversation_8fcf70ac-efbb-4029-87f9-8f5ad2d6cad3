import { PromptTemplate } from "@langchain/core/prompts";

export const serviceRequestConfirmationPrompt = PromptTemplate.fromTemplate(`
Message: "{message}"
Is this message a confirmation to proceed with booking a service request? Reply with true or false.
`);

export const userConfirmationPrompt = PromptTemplate.fromTemplate(`
User message: "{message}"

Is this message a confirmation to proceed with booking/creating a service request?

Examples of confirmations:
- "yes"
- "yes please book this"
- "book it"
- "please book"
- "confirm"
- "proceed"
- "yes please book it"
- "book this service"

Examples of non-confirmations:
- "2020 Honda Civic" (providing information)
- "123 Main Street" (providing address)
- "tomorrow at 2pm" (providing time)
- "no"
- "cancel"

Reply with true or false only.
`);
