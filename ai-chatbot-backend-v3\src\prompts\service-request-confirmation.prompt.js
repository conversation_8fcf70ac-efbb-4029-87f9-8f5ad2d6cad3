import { PromptTemplate } from "@langchain/core/prompts";

export const serviceRequestConfirmationPrompt = PromptTemplate.fromTemplate(`
Message: "{message}"
Is this message a confirmation to proceed with booking a service request? Reply with true or false.
`);

export const userConfirmationPrompt = PromptTemplate.fromTemplate(`
Message: "{message}"

Is this a booking confirmation? Reply true/false.

Confirmations: yes, book it, confirm, proceed, yes please book
Not confirmations: vehicle details, addresses, times, no, cancel
`);
