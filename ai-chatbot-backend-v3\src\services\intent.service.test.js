import { IntentService } from "./intent.service.js";

async function testIntentExtraction() {
  const tests = [
    {
      description: "Car details in service request flow",
      message: "Chevrolet Pilot 2020 Limited, 3.5L V6, CVT",
      context:
        "The user is booking a service request. Please provide the make, model, and year of your car.",
      expectedDomain: "service_request",
    },
    {
      description: "Assign provider in service request flow",
      message: "assign <PERSON>jay",
      context:
        "Do you have a preferred provider in mind, or should I assign one?",
      expectedDomain: "service_request",
    },
    {
      description: "Special instructions in service request flow",
      message: "handle with care",
      context: "Do you have any special instructions for the provider?",
      expectedDomain: "service_request",
    },
    {
      description: "No special instructions in service request flow",
      message: "no special instructions",
      context: "Do you have any special instructions for the provider?",
      expectedDomain: "service_request",
    },
    {
      description: "General greeting outside flow",
      message: "hi there",
      context: "",
      expectedDomain: "general",
    },
    {
      description: "Booking confirmation in service request flow",
      message: "yes, please book my service request",
      context:
        "Here is a summary of your service request. Would you like to confirm and book this service?",
      expectedDomain: "service_request",
    },
  ];

  for (const test of tests) {
    const result = await IntentService.extractIntentAndQuery(
      test.message,
      test.context
    );
    const pass = result.domain === test.expectedDomain;
    console.log(
      `[${pass ? "PASS" : "FAIL"}] ${test.description}\n  Message: ${
        test.message
      }\n  Context: ${test.context}\n  Expected: ${
        test.expectedDomain
      }\n  Got: ${result.domain}\n`
    );
  }
}

// Run the test suite
if (require.main === module) {
  testIntentExtraction();
}
