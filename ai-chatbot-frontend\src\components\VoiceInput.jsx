import React from "react";
import styles from "../styles/ChatApp.module.css";
import { USER_MESSAGES } from "../utils/messages.js";

const VoiceInput = ({
  listening,
  onStartListening,
  onStopListening,
  micPermissionDenied,
}) => {
  const handleClick = () => {
    if (listening) {
      onStopListening();
    } else {
      if (micPermissionDenied) {
        alert(USER_MESSAGES.MICROPHONE_BLOCKED);
        return;
      }
      onStartListening();
    }
  };

  return (
    <button
      type="button"
      onClick={handleClick}
      className={`${styles.voiceButton} ${listening ? styles.listening : ""} ${
        micPermissionDenied ? styles.denied : ""
      }`}
      title={
        micPermissionDenied
          ? USER_MESSAGES.VOICE_BUTTON_TITLE_DENIED
          : listening
          ? USER_MESSAGES.VOICE_BUTTON_TITLE_LISTENING
          : USER_MESSAGES.VOICE_BUTTON_TITLE_START
      }
    >
      {listening ? (
        <div className={styles.listeningIndicator}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <rect
              x="6"
              y="6"
              width="12"
              height="12"
              rx="2"
              fill="currentColor"
            />
          </svg>
          <div className={styles.voicePulse}></div>
        </div>
      ) : (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path
            d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"
            fill="currentColor"
          />
          <path
            d="M19 10v2a7 7 0 0 1-14 0v-2M12 19v4M8 23h8"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          {micPermissionDenied && (
            <line
              x1="23"
              y1="1"
              x2="1"
              y2="23"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
            />
          )}
        </svg>
      )}
    </button>
  );
};

export default VoiceInput;
