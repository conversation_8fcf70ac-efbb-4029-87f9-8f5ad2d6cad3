import { IntentService } from "../services/intent.service.js";
import { processProviderQuery } from "../services/provider.service.js";
import { GeneralConversationService } from "../services/general-conversation.service.js";
import { ServiceRequestService } from "../services/service-request.service.js";
import * as chatMemory from "../services/chat-memory.service.js";

export const intentTool = {
  name: "intentRouter",
  description:
    "Routes user messages to the correct domain service based on detected domain.",
  /**
   * @param {Object} input
   * @param {string} input.message - The user's message.
   * @param {string} [input.userId] - Optional user ID for context.
   * @param {string} [input.sessionId] - Optional session ID for chat memory.
   * @returns {Promise<Object>} The routed service's response.
   */
  async call({ message, userId, sessionId }) {
    console.log("[AGENT] intentTool is handling the request.");

    // // Get the last bot message from chat history for context
    // let lastBotMessage = "";
    // try {
    //   const history = await chatMemory.getHistory(sessionId);
    //   if (history && history.length > 0) {
    //     // Find the last AI message
    //     for (let i = history.length - 1; i >= 0; i--) {
    //       const msg = history[i];
    //       const isAI = msg._getType && msg._getType() === "ai";
    //       if (isAI) {
    //         lastBotMessage = msg.content || "";
    //         break;
    //       }
    //     }
    //   }
    // } catch (error) {
    //   console.warn(
    //     "[WARNING] Could not retrieve last bot message:",
    //     error.message
    //   );
    // }

    console.log("[AGENT] lastBotMessage....", lastBotMessage);

    return await GeneralConversationService.handleGeneralConversation(
      message,
      userId,
      sessionId
    );

    // // Pass lastBotMessage as context and userId for intent extraction
    // const extracted = await IntentService.extractIntentAndQuery(
    //   message,
    //   lastBotMessage,
    //   userId
    // );
    // console.log("extracted.....", extracted);

    // switch (extracted.domain) {
    //   case "provider":
    //     return await processProviderQuery({
    //       ...extracted,
    //       message,
    //       userId,
    //       sessionId,
    //     });
    //   case "service_request": {
    //     // Call handleLLMFlow, which manages collected data internally
    //     const response = await ServiceRequestService.handleLLMFlow({
    //       userId,
    //       message,
    //       sessionId,
    //     });
    //     return response;
    //   }
    //   // case "service":
    //   //   // Route service domain to service_request handling
    //   //   console.log(
    //   //     "[INFO] Routing 'service' domain to service_request handling"
    //   //   );
    //   //   const response = await ServiceRequestService.handleLLMFlow({
    //   //     userId,
    //   //     message,
    //   //     sessionId,
    //   //   });
    //   //   return response;
    //   // Add more domains as needed
    //   default:
    //     return await GeneralConversationService.handleGeneralConversation(
    //       message,
    //       userId,
    //       sessionId
    //     );
    // }
  },
};
