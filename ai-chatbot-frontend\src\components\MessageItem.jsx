import React from "react";
import MessageFormatter from "../utils/messageFormatter.jsx";
import styles from "../styles/ChatApp.module.css";

const TypingIndicator = () => (
  <div className={styles.typingIndicator}>
    <div className={styles.typingDots}>
      <span></span>
      <span></span>
      <span></span>
    </div>
    <span className={styles.typingText}>Analyzing data, please wait...</span>
  </div>
);

const MessageItem = ({ message, isLast }) => {
  const isAI = message.sender === "ai";

  return (
    <div
      className={`${styles.messageWrapper} ${
        isAI ? styles.aiMessage : styles.userMessage
      }`}
      ref={isLast ? (el) => el?.scrollIntoView({ behavior: "smooth" }) : null}
    >
      {isAI && (
        <div className={styles.messageAvatar}>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" fill="url(#msgGradient)" />
            <path
              d="M9.5 9.5C9.5 8.11929 10.6193 7 12 7C13.3807 7 14.5 8.11929 14.5 9.5"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
            <circle cx="12" cy="15" r="1" fill="white" />
            <defs>
              <linearGradient
                id="msgGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="#8B5CF6" />
                <stop offset="100%" stopColor="#A855F7" />
              </linearGradient>
            </defs>
          </svg>
        </div>
      )}

      <div className={styles.messageCard}>
        <div className={styles.messageContent}>
          {message.isLoading ? (
            <TypingIndicator />
          ) : (
            <div className={styles.messageText}>
              {isAI ? (
                <MessageFormatter message={message.data} />
              ) : (
                message.text
              )}
            </div>
          )}

          {message.isError && (
            <div className={styles.errorMessage}>
              <span className={styles.errorIcon}>⚠️</span>
              <span>Failed to send message</span>
            </div>
          )}
        </div>

        {isAI && !message.isLoading && (
          <div className={styles.messageActions}>
            <button className={styles.actionBtn} title="Copy message">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <rect
                  x="9"
                  y="9"
                  width="13"
                  height="13"
                  rx="2"
                  ry="2"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                />
                <path
                  d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                />
              </svg>
            </button>
            <button className={styles.actionBtn} title="Like message">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <path
                  d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                />
              </svg>
            </button>
            <button className={styles.actionBtn} title="Dislike message">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <path
                  d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2 2 0 0 1-2.33 2H17"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                />
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageItem;
