import { PromptTemplate } from "@langchain/core/prompts";

export const domainIntentExtractionPrompt = PromptTemplate.fromTemplate(`
{message}

JSON: {"d":"general|provider|service","i":"greeting|provider_listing|specific_provider|provider_filter|help|service_request","q":"query","e":{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null},"qt":"general|detailed|filtered|unknown","f":null}

Examples:
"hi" → {"d":"general","i":"greeting","q":"hi","e":{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null},"qt":"general","f":null}
"list providers" → {"d":"provider","i":"provider_listing","q":"list providers","e":{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null},"qt":"general","f":null}
"5 providers" → {"d":"provider","i":"provider_listing","q":"5 providers","e":{"p":null,"s":null,"l":null,"d":null,"t":null,"pr":null,"o":null},"qt":"general","f":{"limit":5}}
"providers in NY" → {"d":"provider","i":"provider_filter","q":"providers in NY","e":{"p":null,"s":null,"l":"NY","d":null,"t":null,"pr":null,"o":null},"qt":"filtered","f":{"location":"NY"}}
"tell me about Ajay" → {"d":"provider","i":"specific_provider","q":"Ajay","e":{"p":"Ajay","s":null,"l":null,"d":null,"t":null,"pr":null,"o":null},"qt":"detailed","f":null}
"I need an oil change" → {"d":"service","i":"service_request","q":"I need an oil change","e":{"p":null,"s":"oil change","l":null,"d":null,"t":null,"pr":null,"o":null},"qt":"general","f":{"service":"oil change"}}

JSON only.`);
