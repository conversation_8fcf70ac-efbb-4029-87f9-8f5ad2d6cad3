import * as chatMemory from "./chat-memory.service.js";
import { LLMService } from "./llm.service.js";

export class GeneralConversationService {
  static async handleGeneralConversation(message, sessionId) {
    try {
      await chatMemory.appendToHistory(sessionId, "USER", message);
      const historyText = await chatMemory.getFormattedHistory(sessionId);

      const response = await LLMService.generateResponse(message, historyText);
      await chatMemory.appendToHistory(sessionId, "ASSISTANT", response);

      return { message: response, data: null };
    } catch (error) {
      return {
        message:
          "I'm sorry, I'm having trouble processing your request right now. Please try again.",
        data: null,
      };
    }
  }
}
