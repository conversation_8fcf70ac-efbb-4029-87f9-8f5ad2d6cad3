import React from "react";
import styles from "../styles/ChatApp.module.css";

const ChatHeader = () => {
  return (
    <header className={styles.header}>
      <div className={styles.headerContent}>
        <div className={styles.aiAvatar}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" fill="url(#gradient)" />
            <path
              d="M9.5 9.5C9.5 8.11929 10.6193 7 12 7C13.3807 7 14.5 8.11929 14.5 9.5"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
            <circle cx="12" cy="15" r="1" fill="white" />
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8B5CF6" />
                <stop offset="100%" stopColor="#A855F7" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        <div className={styles.headerText}>
          <h1 className={styles.headerTitle}>Revi Assistant</h1>
          <p className={styles.headerSubtitle}>
            AI-powered automotive helper with voice support
          </p>
        </div>
      </div>
    </header>
  );
};

export default ChatHeader;
