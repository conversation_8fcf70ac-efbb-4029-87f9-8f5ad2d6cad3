import { PromptTemplate } from "@langchain/core/prompts";

export const providerListPrompt = PromptTemplate.fromTemplate(`
You are Revi, vehicle services AI. User wants provider list.

{message}

Response (2-3 sentences, friendly):`);

export const providerDetailPrompt = PromptTemplate.fromTemplate(`
You are Revi, vehicle services AI. User wants specific provider info.

{message}

Response (2-3 sentences, friendly):`);

export const generalStructuredPrompt = PromptTemplate.fromTemplate(`
You are Revi, vehicle services AI. Respond friendly to:

{message}
`);
