import express from "express";
import cors from "cors";
import { CORS_ORIGIN } from "./src/config/env.js";
import chatRoutes from "./src/routes/chat.route.js";
import serviceRequestToolRoutes from "./src/routes/service-request-tool.routes.js";

const app = express();

// Middleware
app.use(cors({ origin: CORS_ORIGIN }));
app.use(express.json());

// Routes
app.use("/api", chatRoutes);

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    service: "Chatbot V3",
    version: "3.0.0",
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error("[ERROR] Unhandled error:", err);
  res.status(500).json({
    success: false,
    error: "Internal server error",
    message:
      process.env.NODE_ENV === "development"
        ? err.message
        : "Something went wrong",
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: "Route not found",
    message: `Cannot ${req.method} ${req.path}`,
  });
});

export default app;
