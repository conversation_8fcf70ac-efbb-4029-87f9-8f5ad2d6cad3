export class QueryBuilder {
  static buildProviderQuery(
    filters = {},
    requiredTables = ["User"],
    limit = 10
  ) {
    try {
      console.log("[INFO] Building dynamic provider query with filters:", {
        filters,
        requiredTables,
      });

      // Base query structure
      let query = `
        SELECT 
          u.id,
          u."firstName",
          u."lastName",
          u.email,
          u."mobileNumber"
      `;

      // Add dynamic fields based on required tables
      const joins = [];
      const whereConditions = [];
      const queryParams = [];
      let paramIndex = 1;

      // Always include basic provider conditions
      whereConditions.push(`
        u."role" = 'PROVIDER' 
        AND u."status" = 'active'
        AND u."firstName" IS NOT NULL
        AND u."lastName" IS NOT NULL
        AND u."firstName" != ''
        AND u."lastName" != ''
        AND u.email IS NOT NULL
        AND u.email != ''
      `);

      // Handle service-related tables
      if (
        requiredTables.includes("ProvidedService") ||
        requiredTables.includes("ProvidedServiceType") ||
        requiredTables.includes("ServiceType")
      ) {
        joins.push(`
          LEFT JOIN "ProvidedService" ps ON u.id = ps."userId"
          LEFT JOIN "ProvidedServiceType" pst ON ps.id = pst."providedServiceId"
          LEFT JOIN "ServiceType" st ON pst."serviceTypeId" = st.id
        `);

        query += `,
          st."name" as "serviceName",
          st."description",
          pst."minPrice",
          pst."maxPrice",
          (SELECT COUNT(DISTINCT st_count.id) 
           FROM "ProvidedService" ps_count 
           LEFT JOIN "ProvidedServiceType" pst_count ON ps_count.id = pst_count."providedServiceId" 
           LEFT JOIN "ServiceType" st_count ON pst_count."serviceTypeId" = st_count.id 
           WHERE ps_count."userId" = u.id) as "serviceCount"
        `;

        // Apply service count filter
        if (filters.serviceCount) {
          const { operator, value } = filters.serviceCount;
          const operatorMap = {
            equals: "=",
            greater_than: ">",
            less_than: "<",
            greater_than_or_equal: ">=",
            less_than_or_equal: "<=",
          };

          if (operatorMap[operator]) {
            whereConditions.push(`
              (SELECT COUNT(DISTINCT st2.id) FROM "ProvidedService" ps2 
               LEFT JOIN "ProvidedServiceType" pst2 ON ps2.id = pst2."providedServiceId" 
               LEFT JOIN "ServiceType" st2 ON pst2."serviceTypeId" = st2.id 
               WHERE ps2."userId" = u.id) ${operatorMap[operator]} $${paramIndex}
            `);
            queryParams.push(value);
            paramIndex++;
          }
        }

        // Apply specific services filter
        if (filters.services && filters.services.length > 0) {
          filters.services.forEach((service) => {
            whereConditions.push(`
              EXISTS (
                SELECT 1 FROM "ProvidedService" ps3 
                LEFT JOIN "ProvidedServiceType" pst3 ON ps3.id = pst3."providedServiceId" 
                LEFT JOIN "ServiceType" st3 ON pst3."serviceTypeId" = st3.id 
                WHERE ps3."userId" = u.id AND LOWER(st3."name") LIKE LOWER($${paramIndex})
              )
            `);
            queryParams.push(`%${service}%`);
            paramIndex++;
          });
        }

        // Apply price range filter
        if (filters.priceRange) {
          if (filters.priceRange.min !== undefined) {
            whereConditions.push(`pst."minPrice" >= $${paramIndex}`);
            queryParams.push(filters.priceRange.min);
            paramIndex++;
          }
          if (filters.priceRange.max !== undefined) {
            whereConditions.push(`pst."maxPrice" <= $${paramIndex}`);
            queryParams.push(filters.priceRange.max);
            paramIndex++;
          }
        }
      }

      // Handle location table
      if (requiredTables.includes("ProviderLocation")) {
        joins.push(`LEFT JOIN "ProviderLocation" pl ON u.id = pl."userId"`);
        query += `, pl."city" as "location", pl."address"`;

        if (filters.location) {
          whereConditions.push(`LOWER(pl."city") LIKE LOWER($${paramIndex})`);
          queryParams.push(`%${filters.location}%`);
          paramIndex++;
        }
      }

      // Handle rating table
      if (requiredTables.includes("ProviderRating")) {
        joins.push(`LEFT JOIN "ProviderRating" pr ON u.id = pr."userId"`);
        query += `, pr."rating" as "providerRating", pr."reviewCount"`;

        if (filters.rating) {
          const { operator, value } = filters.rating;
          const operatorMap = {
            equals: "=",
            greater_than: ">",
            less_than: "<",
          };

          if (operatorMap[operator]) {
            whereConditions.push(
              `pr."rating" ${operatorMap[operator]} $${paramIndex}`
            );
            queryParams.push(value);
            paramIndex++;
          }
        }
      }

      // Handle availability/status table
      if (requiredTables.includes("ProviderStatus")) {
        joins.push(`LEFT JOIN "ProviderStatus" ps ON u.id = ps."userId"`);
        query += `, ps."status" as "availability", ps."lastSeen"`;

        if (filters.availability) {
          whereConditions.push(`LOWER(ps."status") = LOWER($${paramIndex})`);
          queryParams.push(filters.availability);
          paramIndex++;
        }
      }

      // Handle experience/profile table
      if (requiredTables.includes("ProviderProfile")) {
        joins.push(`LEFT JOIN "ProviderProfile" pp ON u.id = pp."userId"`);
        query += `, pp."experienceYears" as "experience", pp."specialization"`;

        if (filters.experience) {
          const { operator, value } = filters.experience;
          const operatorMap = {
            equals: "=",
            greater_than: ">",
            less_than: "<",
          };

          if (operatorMap[operator]) {
            whereConditions.push(
              `pp."experienceYears" ${operatorMap[operator]} $${paramIndex}`
            );
            queryParams.push(value);
            paramIndex++;
          }
        }
      }

      // Handle availability tables (day and time slots)
      if (requiredTables.includes("ProvidedServiceAvailability")) {
        joins.push(
          `LEFT JOIN "ProvidedServiceAvailability" psa ON u.id = psa."userId"`
        );
        query += `, psa."dayOfWeek" as "availableDay"`;

        if (filters.availabilityDay) {
          whereConditions.push(
            `LOWER(psa."dayOfWeek") = LOWER($${paramIndex})`
          );
          queryParams.push(filters.availabilityDay);
          paramIndex++;
        }
      }

      // Handle availability time slots
      if (requiredTables.includes("ProvidedServiceAvailabilitySlot")) {
        // Make sure we have the availability table joined first
        if (!requiredTables.includes("ProvidedServiceAvailability")) {
          joins.push(
            `LEFT JOIN "ProvidedServiceAvailability" psa ON u.id = psa."userId"`
          );
          query += `, psa."dayOfWeek" as "availableDay"`;
        }

        joins.push(
          `LEFT JOIN "ProvidedServiceAvailabilitySlot" psas ON psa.id = psas."availabilityId"`
        );
        query += `, psas."startTime" as "slotStartTime", psas."endTime" as "slotEndTime"`;

        if (filters.availabilityTime) {
          const { startTime, endTime } = filters.availabilityTime;
          if (startTime) {
            whereConditions.push(`psas."startTime" <= $${paramIndex}`);
            queryParams.push(startTime);
            paramIndex++;
          }
          if (endTime) {
            whereConditions.push(`psas."endTime" >= $${paramIndex}`);
            queryParams.push(endTime);
            paramIndex++;
          }
        }
      }

      // Handle cancellation policy
      if (requiredTables.includes("ProvidedServiceCancellationPolicy")) {
        joins.push(
          `LEFT JOIN "ProvidedServiceCancellationPolicy" pscp ON u.id = pscp."userId"`
        );
        query += `, pscp."cancellationFee" as "cancellationFee"`;

        if (filters.cancellationFee) {
          const { operator, value } = filters.cancellationFee;
          const operatorMap = {
            equals: "=",
            greater_than: ">",
            less_than: "<",
          };

          if (operatorMap[operator]) {
            whereConditions.push(
              `pscp."cancellationFee" ${operatorMap[operator]} $${paramIndex}`
            );
            queryParams.push(value);
            paramIndex++;
          }
        }
      }

      // Build the complete query
      query += `
        FROM "User" u
        ${joins.join("\n")}
        WHERE ${whereConditions.join(" AND ")}
        ORDER BY u."firstName"
        LIMIT $${paramIndex};
      `;
      queryParams.push(limit * 3); // Get more to group properly

      console.log("[SUCCESS] Dynamic query built successfully:", {
        queryLength: query.length,
        paramCount: queryParams.length,
        requiredTables,
      });

      return {
        query: query.trim(),
        params: queryParams,
        requiredTables,
      };
    } catch (error) {
      console.error("[ERROR] Error building dynamic query:", error);
      throw new Error("Failed to build dynamic query");
    }
  }

  static getTableSchema() {
    return {
      User: {
        description: "Main user table with provider information",
        fields: [
          "id",
          "firstName",
          "lastName",
          "email",
          "mobileNumber",
          "role",
          "status",
        ],
      },
      ProvidedService: {
        description: "Links providers to their services",
        fields: ["id", "userId", "serviceId"],
      },
      ProvidedServiceType: {
        description: "Service pricing and details",
        fields: [
          "id",
          "providedServiceId",
          "serviceTypeId",
          "minPrice",
          "maxPrice",
        ],
      },
      ServiceType: {
        description: "Available service types",
        fields: ["id", "name", "description"],
      },
      ProviderLocation: {
        description: "Provider location information",
        fields: ["id", "userId", "city", "address", "latitude", "longitude"],
      },
      ProviderRating: {
        description: "Provider ratings and reviews",
        fields: ["id", "userId", "rating", "reviewCount", "averageRating"],
      },
      ProviderStatus: {
        description: "Provider availability status",
        fields: ["id", "userId", "status", "lastSeen", "isOnline"],
      },
      ProviderProfile: {
        description: "Provider experience and profile",
        fields: [
          "id",
          "userId",
          "experienceYears",
          "specialization",
          "certifications",
        ],
      },
      ProvidedServiceAvailability: {
        description: "Provider availability by day of week",
        fields: ["id", "userId", "dayOfWeek"],
      },
      ProvidedServiceAvailabilitySlot: {
        description: "Time slots for provider availability",
        fields: ["id", "availabilityId", "startTime", "endTime"],
      },
      ProvidedServiceCancellationPolicy: {
        description: "Provider cancellation policies",
        fields: ["id", "userId", "cancellationFee", "startTime", "endTime"],
      },
    };
  }
}
