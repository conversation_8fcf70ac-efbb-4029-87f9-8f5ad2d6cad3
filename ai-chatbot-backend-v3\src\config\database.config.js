export const DATABASE_CONFIG = {
  type: "postgres",
  url: process.env.DATABASE_URL,
  synchronize: false,
  logging: true,
  entities: ["User", "Provider", "ChatSession", "ChatMessage"],
  ssl: {
    require: true,
    rejectUnauthorized: false,
  },
  customDescriptions: {
    User: "Contains user information including customers and service providers",
    Provider: "Contains provider information and services",
    ChatSession: "Contains chat session information",
    ChatMessage: "Contains chat messages for sessions",
  },
};
