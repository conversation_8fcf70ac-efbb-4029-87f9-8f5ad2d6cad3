export class JsonUtils {
  /**
   * Parse JSON response from LLM with robust error handling
   * @param {string} response - The raw response from LLM
   * @param {string} operation - Operation name for logging
   * @returns {object|null} - Parsed JSON object or null if parsing fails
   */
  static parseJsonResponse(response, operation = "LLM Response") {
    try {
      if (typeof response !== "string") {
        console.warn(`[WARNING] ${operation}: Response is not a string`, {
          response,
        });
        return null;
      }

      // Remove markdown code blocks
      let cleaned = response.replace(/```json\s*/g, "").replace(/```\s*$/g, "");
      cleaned = cleaned.trim();

      // Try to parse as JSON
      const parsed = JSON.parse(cleaned);
      console.log(`[SUCCESS] ${operation}: JSON parsed successfully`, {
        parsed,
      });
      return parsed;
    } catch (parseError) {
      console.error(`[ERROR] ${operation}: JSON parsing failed`, {
        error: parseError.message,
        rawResponse: response.substring(0, 200) + "...",
      });

      // Try to extract <PERSON><PERSON><PERSON> from the response using regex
      try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const extracted = JSON.parse(jsonMatch[0]);
          console.log(`[SUCCESS] ${operation}: JSON extracted using regex`, {
            extracted,
          });
          return extracted;
        }
      } catch (extractError) {
        console.error(`[ERROR] ${operation}: JSON extraction also failed`, {
          error: extractError.message,
        });
      }

      return null;
    }
  }

  /**
   * Safely parse JSON with fallback
   * @param {string} jsonString - JSON string to parse
   * @param {any} fallback - Fallback value if parsing fails
   * @returns {any} - Parsed object or fallback
   */
  static safeParse(jsonString, fallback = null) {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.warn("[WARNING] JSON safe parse failed, using fallback", {
        error: error.message,
      });
      return fallback;
    }
  }

  /**
   * Validate if a string is valid JSON
   * @param {string} jsonString - String to validate
   * @returns {boolean} - True if valid JSON
   */
  static isValidJson(jsonString) {
    try {
      JSON.parse(jsonString);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Extract JSON object from text that might contain other content
   * @param {string} text - Text that might contain JSON
   * @returns {object|null} - Extracted JSON object or null
   */
  static extractJsonFromText(text) {
    try {
      // Look for JSON object pattern
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      return null;
    } catch (error) {
      console.warn("[WARNING] Failed to extract JSON from text", {
        error: error.message,
      });
      return null;
    }
  }

  /**
   * Clean and format JSON response for consistent output
   * @param {object} data - Data to format
   * @param {number} maxLength - Maximum length for string truncation
   * @returns {object} - Cleaned and formatted data
   */
  static formatForLogging(data, maxLength = 200) {
    try {
      const jsonString = JSON.stringify(data);
      return {
        data:
          jsonString.length > maxLength
            ? jsonString.substring(0, maxLength) + "..."
            : jsonString,
        length: jsonString.length,
      };
    } catch (error) {
      return { data: "[Circular or non-serializable object]", length: 0 };
    }
  }
}
