import { PromptTemplate } from "@langchain/core/prompts";

export const toolSelectionPrompt = PromptTemplate.fromTemplate(`
Current message: "{message}"
Recent conversation: "{history}"

Context-aware tool selection:
- If conversation mentions service requests/booking → serviceRequestTool
- Car details (Honda Civic, 2020 Toyota, BMW X5, etc.) → serviceRequestTool
- Confirmations (yes, no, confirm, ok) → serviceRequestTool
- Addresses, times, appointments → serviceRequestTool
- Provider questions → providerTool
- General chat → intentTool

Tool name only:`);

export const providerExtractionPrompt = PromptTemplate.fromTemplate(`
Extract the provider name from this message: "{message}"
If no specific provider name is mentioned, respond with "unknown".
Respond with ONLY the provider name and nothing else.
`);
