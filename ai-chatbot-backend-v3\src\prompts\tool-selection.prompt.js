import { PromptTemplate } from "@langchain/core/prompts";

export const toolSelectionPrompt = PromptTemplate.fromTemplate(`
Message: "{message}"

Rules:
- "yes", "no", "confirm" = serviceRequestTool
- Vehicle details, addresses, times = serviceRequestTool
- Service booking requests = serviceRequestTool
- Provider questions = providerTool
- General chat = intentTool

Tool:`);

export const providerExtractionPrompt = PromptTemplate.fromTemplate(`
Extract the provider name from this message: "{message}"
If no specific provider name is mentioned, respond with "unknown".
Respond with ONLY the provider name and nothing else.
`);
