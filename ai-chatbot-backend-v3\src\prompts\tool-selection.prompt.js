import { PromptTemplate } from "@langchain/core/prompts";

export const toolSelectionPrompt = PromptTemplate.fromTemplate(`
You are a tool selection assistant. Based on the user's message, determine which tool should be used to handle their request.

Available tools:
1. "serviceRequestTool" - Use for:
   - Vehicle service requests, maintenance, repairs, booking appointments, scheduling services
   - When user mentions vehicle details (make, model, year, etc.)
   - Confirmations in service request context (yes, no, confirm, proceed)
   - Providing missing information for service requests (addresses, vehicle details, etc.)
   - Any continuation of an ongoing service request conversation

2. "providerTool" - Use when user asks about specific providers, technicians, or service provider information

3. "intentTool" - Use for general conversation, questions about services, or anything else not covered by the above tools

IMPORTANT CONTEXT RULES:
- If the message contains confirmations like "yes", "no", "confirm", "proceed" → likely serviceRequestTool
- If the message contains vehicle details, addresses, or service-related information → serviceRequestTool
- If the message is a short response in what appears to be an ongoing conversation → serviceRequestTool
- Only use intentTool for completely new conversations or general questions

User message: "{message}"

Respond with ONLY the tool name (serviceRequestTool, providerTool, or intentTool) and nothing else.
`);

export const providerExtractionPrompt = PromptTemplate.fromTemplate(`
Extract the provider name from this message: "{message}"
If no specific provider name is mentioned, respond with "unknown".
Respond with ONLY the provider name and nothing else.
`);
