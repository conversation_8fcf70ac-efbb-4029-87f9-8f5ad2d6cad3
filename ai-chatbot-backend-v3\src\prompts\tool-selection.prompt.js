import { PromptTemplate } from "@langchain/core/prompts";

export const toolSelectionPrompt = PromptTemplate.fromTemplate(`
You are a tool selection assistant. Based on the user's message, determine which tool should be used to handle their request.

Available tools:
1. "serviceRequestTool" - Use for vehicle service requests, maintenance, repairs, booking appointments, scheduling services, or when user mentions vehicle details (make, model, year, etc.)
2. "providerTool" - Use when user asks about specific providers, technicians, or service provider information
3. "intentTool" - Use for general conversation, questions about services, or anything else not covered by the above tools

User message: "{message}"

Respond with ONLY the tool name (serviceRequestTool, providerTool, or intentTool) and nothing else.
`);

export const providerExtractionPrompt = PromptTemplate.fromTemplate(`
Extract the provider name from this message: "{message}"
If no specific provider name is mentioned, respond with "unknown".
Respond with ONLY the provider name and nothing else.
`);
