import { PromptTemplate } from "@langchain/core/prompts";

export const toolSelectionPrompt = PromptTemplate.fromTemplate(`
Select tool for: "{message}"

Tools:
- serviceRequestTool: service booking, vehicle details, confirmations, addresses
- providerTool: provider questions
- intentTool: general chat

Reply tool name only.
`);

export const providerExtractionPrompt = PromptTemplate.fromTemplate(`
Extract the provider name from this message: "{message}"
If no specific provider name is mentioned, respond with "unknown".
Respond with ONLY the provider name and nothing else.
`);
