/**
 * Simple token counter utility for monitoring LLM usage
 */

class TokenCounter {
  constructor() {
    this.totalTokens = 0;
    this.requestCount = 0;
    this.startTime = Date.now();
  }

  // Rough token estimation (1 token ≈ 4 characters for English)
  estimateTokens(text) {
    if (!text) return 0;
    return Math.ceil(text.length / 4);
  }

  // Log token usage for a request
  logRequest(prompt, response, operation = 'LLM_CALL') {
    const promptTokens = this.estimateTokens(prompt);
    const responseTokens = this.estimateTokens(response);
    const totalRequestTokens = promptTokens + responseTokens;
    
    this.totalTokens += totalRequestTokens;
    this.requestCount++;
    
    console.log(`[TOKENS] ${operation}:`);
    console.log(`[TOKENS] - Prompt: ${promptTokens} tokens`);
    console.log(`[TOKENS] - Response: ${responseTokens} tokens`);
    console.log(`[TOKENS] - Request Total: ${totalRequestTokens} tokens`);
    console.log(`[TOKENS] - Session Total: ${this.totalTokens} tokens (${this.requestCount} requests)`);
    
    return {
      promptTokens,
      responseTokens,
      totalRequestTokens,
      sessionTotal: this.totalTokens,
      requestCount: this.requestCount
    };
  }

  // Get session statistics
  getStats() {
    const uptime = Date.now() - this.startTime;
    return {
      totalTokens: this.totalTokens,
      requestCount: this.requestCount,
      averageTokensPerRequest: this.requestCount > 0 ? Math.round(this.totalTokens / this.requestCount) : 0,
      uptimeMs: uptime,
      tokensPerMinute: uptime > 0 ? Math.round((this.totalTokens * 60000) / uptime) : 0
    };
  }

  // Reset counters
  reset() {
    this.totalTokens = 0;
    this.requestCount = 0;
    this.startTime = Date.now();
    console.log(`[TOKENS] Counter reset`);
  }
}

// Global instance
const tokenCounter = new TokenCounter();

export { tokenCounter };
