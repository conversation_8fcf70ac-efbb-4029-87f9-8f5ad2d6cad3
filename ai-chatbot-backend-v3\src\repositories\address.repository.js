import { databaseService } from "../db/connection.js";

export class AddressRepository {
  /**
   * Check if an address exists in the database for a specific user
   * @param {Object} addressData - Address data to check
   * @param {number} userId - User ID
   * @returns {Object} - { exists: boolean, address: Object|null }
   */
  static async checkAddressExists(addressData, userId) {
    try {
      console.log(`[INFO] Checking if address exists for user ${userId}:`, addressData);
      
      const query = `
        SELECT * FROM "Address"
        WHERE "userId" = $1
          AND "addressLine1" = $2
          AND "city" = $3
          AND "state" = $4
          AND "zipcode" = $5
          AND "addressType" = $6
        LIMIT 1;
      `;
      
      const values = [
        userId,
        addressData?.address || null,
        addressData?.city || null,
        addressData?.state || null,
        addressData?.zipCode || addressData?.zip || null,
        addressData?.addressType || "home",
      ];

      const result = await databaseService.getDataSource().query(query, values);
      
      const exists = result.length > 0;
      console.log(`[INFO] Address exists check result: ${exists}`);
      
      return {
        exists,
        address: exists ? result[0] : null
      };
    } catch (error) {
      console.error(`[ERROR] Error checking address existence:`, error);
      throw new Error("Failed to check address existence");
    }
  }

  /**
   * Check if a similar address exists (fuzzy matching)
   * @param {Object} addressData - Address data to check
   * @param {number} userId - User ID
   * @returns {Array} - Array of similar addresses
   */
  static async findSimilarAddresses(addressData, userId) {
    try {
      console.log(`[INFO] Finding similar addresses for user ${userId}:`, addressData);
      
      const query = `
        SELECT * FROM "Address"
        WHERE "userId" = $1
          AND (
            "addressLine1" ILIKE $2
            OR "city" ILIKE $3
            OR "zipcode" = $4
          )
        ORDER BY "createdAt" DESC
        LIMIT 5;
      `;
      
      const values = [
        userId,
        `%${addressData?.address || ''}%`,
        `%${addressData?.city || ''}%`,
        addressData?.zipCode || addressData?.zip || null
      ];

      const result = await databaseService.getDataSource().query(query, values);
      
      console.log(`[INFO] Found ${result.length} similar addresses`);
      
      return result;
    } catch (error) {
      console.error(`[ERROR] Error finding similar addresses:`, error);
      throw new Error("Failed to find similar addresses");
    }
  }

  /**
   * Create a new address in the database
   * @param {Object} addressData - Address data to create
   * @param {number} userId - User ID
   * @returns {Object} - Created address
   */
  static async createAddress(addressData, userId) {
    try {
      console.log(`[INFO] Creating new address for user ${userId}:`, addressData);
      
      const query = `
        INSERT INTO "Address" (
          "userId", "addressLine1", "city", "state", "zipcode", "addressType", "createdAt", "updatedAt"
        ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW()) RETURNING *;
      `;
      
      const values = [
        userId,
        addressData?.address || null,
        addressData?.city || null,
        addressData?.state || null,
        addressData?.zipCode || addressData?.zip || null,
        addressData?.addressType || "home",
      ];

      const result = await databaseService.getDataSource().query(query, values);
      
      console.log(`[SUCCESS] Created new address:`, result[0]);
      
      return result[0];
    } catch (error) {
      console.error(`[ERROR] Error creating address:`, error);
      throw new Error("Failed to create address");
    }
  }

  /**
   * Get all addresses for a user
   * @param {number} userId - User ID
   * @returns {Array} - Array of user's addresses
   */
  static async getUserAddresses(userId) {
    try {
      console.log(`[INFO] Getting all addresses for user ${userId}`);
      
      const query = `
        SELECT * FROM "Address"
        WHERE "userId" = $1
        ORDER BY "createdAt" DESC;
      `;

      const result = await databaseService.getDataSource().query(query, [userId]);
      
      console.log(`[INFO] Found ${result.length} addresses for user ${userId}`);
      
      return result;
    } catch (error) {
      console.error(`[ERROR] Error getting user addresses:`, error);
      throw new Error("Failed to get user addresses");
    }
  }

  /**
   * Validate address format and completeness
   * @param {Object} addressData - Address data to validate
   * @returns {Object} - { isValid: boolean, missingFields: Array, errors: Array }
   */
  static validateAddress(addressData) {
    const missingFields = [];
    const errors = [];

    if (!addressData?.address || addressData.address.trim() === '') {
      missingFields.push('address');
    }

    if (!addressData?.city || addressData.city.trim() === '') {
      missingFields.push('city');
    }

    if (!addressData?.state || addressData.state.trim() === '') {
      missingFields.push('state');
    }

    const zipCode = addressData?.zipCode || addressData?.zip;
    if (!zipCode || zipCode.trim() === '') {
      missingFields.push('zipCode');
    } else if (!/^\d{5}(-\d{4})?$/.test(zipCode.trim())) {
      errors.push('Invalid zip code format. Expected format: 12345 or 12345-6789');
    }

    return {
      isValid: missingFields.length === 0 && errors.length === 0,
      missingFields,
      errors
    };
  }
}
