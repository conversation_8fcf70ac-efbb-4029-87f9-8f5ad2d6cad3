/* Clean Purple Theme Design System */
:root {
  /* Color Palette */
  --primary-purple: #8b5cf6;
  --primary-purple-light: #a855f7;
  --primary-purple-dark: #7c3aed;
  --secondary-purple: #e0e7ff;
  --accent-purple: #ddd6fe;

  --bg-primary: #f8fafc;
  --bg-secondary: #f1f5f9;
  --bg-tertiary: #e2e8f0;
  --bg-card: #ffffff;
  --bg-purple-light: #faf5ff;

  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --text-white: #ffffff;

  --border-light: #e2e8f0;
  --border-medium: #cbd5e1;
  --border-purple: #c4b5fd;

  --success-green: #10b981;
  --error-red: #ef4444;
  --warning-orange: #f59e0b;

  /* Spacing */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-3xl: 24px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg:
    0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl:
    0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-purple: 0 4px 14px 0 rgb(139 92 246 / 0.15);

  /* Typography */
  --font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue",
    Arial, sans-serif;

  /* Transitions */
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* App Wrapper */
.appWrapper {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chatContainer {
  width: 100%;
  max-width: 600px;
  height: 700px;
  background: var(--bg-card);
  border-radius: var(--radius-3xl);
  box-shadow: var(--shadow-xl);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid var(--border-light);
}

/* Header */
.header {
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-light);
  padding: var(--space-6);
  flex-shrink: 0;
}

.headerContent {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.aiAvatar {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.headerText {
  flex: 1;
}

.headerTitle {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.headerSubtitle {
  font-size: 14px;
  color: var(--text-secondary);
}

/* Messages Area */
.messagesArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--bg-primary);
}

.messagesContent {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-6);
  scroll-behavior: smooth;
}

.messagesContent::-webkit-scrollbar {
  width: 6px;
}

.messagesContent::-webkit-scrollbar-track {
  background: transparent;
}

.messagesContent::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius-full);
}

.messagesContent::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Welcome State */
.welcomeState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--space-12) var(--space-6);
  min-height: 300px;
}

.welcomeAvatar {
  margin-bottom: var(--space-6);
}

.welcomeTitle {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.welcomeDescription {
  font-size: 16px;
  color: var(--text-secondary);
  max-width: 400px;
  margin-bottom: var(--space-8);
  line-height: 1.6;
}

.featureCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-4);
  width: 100%;
  max-width: 400px;
}

.featureCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  transition: var(--transition);
}

.featureCard:hover {
  border-color: var(--border-purple);
  box-shadow: var(--shadow-purple);
  transform: translateY(-2px);
}

.featureIcon {
  font-size: 20px;
}

.featureText {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  text-align: center;
}

/* Message Wrapper */
.messageWrapper {
  margin-bottom: var(--space-6);
  display: flex;
  gap: var(--space-3);
  animation: slideUp 0.3s ease-out;
}

.userMessage {
  flex-direction: row-reverse;
}

.userMessage .messageCard {
  background: var(--primary-purple);
  color: var(--text-white);
  border: none;
}

.messageAvatar {
  flex-shrink: 0;
  margin-top: var(--space-1);
}

.messageCard {
  flex: 1;
  max-width: 80%;
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.messageContent {
  padding: var(--space-5);
}

.messageText {
  font-size: 15px;
  line-height: 1.6;
  color: inherit;
}

.messageActions {
  display: flex;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-5);
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

.actionBtn {
  background: none;
  border: none;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  color: var(--text-tertiary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.actionBtn:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

/* Typing Indicator */
.typingIndicator {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.typingDots {
  display: flex;
  gap: 4px;
}

.typingDots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-purple);
  animation: typingBounce 1.4s ease-in-out infinite both;
}

.typingDots span:nth-child(1) {
  animation-delay: -0.32s;
}
.typingDots span:nth-child(2) {
  animation-delay: -0.16s;
}

.typingText {
  font-size: 14px;
  color: var(--text-secondary);
  font-style: italic;
}

@keyframes typingBounce {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Error Message */
.errorMessage {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-3);
  padding: var(--space-2) var(--space-3);
  background: rgba(239, 68, 68, 0.1);
  border-radius: var(--radius-md);
  font-size: 14px;
  color: var(--error-red);
}

.errorIcon {
  font-size: 12px;
}

/* Bottom Panel */
.bottomPanel {
  background: var(--bg-card);
  border-top: 1px solid var(--border-light);
  flex-shrink: 0;
}

/* Speech Controls */
.speechControlsCard {
  margin: var(--space-4);
  padding: var(--space-4);
  background: var(--bg-purple-light);
  border: 1px solid var(--border-purple);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.speechInfo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.speechIcon {
  color: var(--primary-purple);
  display: flex;
  align-items: center;
}

.speechText {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.speechLabel {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
}

.speechStatus {
  font-size: 11px;
  color: var(--text-secondary);
}

.speechActions {
  display: flex;
  gap: var(--space-2);
}

.speechButton {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  background: var(--bg-card);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  border: 1px solid var(--border-medium);
}

.speechButton:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Input Section */
.inputSection {
  padding: var(--space-4) var(--space-6) var(--space-6);
}

.inputForm {
  width: 100%;
}

.inputContainer {
  display: flex;
  align-items: center;
  background: var(--bg-card);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--space-3);
  gap: var(--space-3);
  transition: var(--transition);
}

.inputContainer:focus-within {
  border-color: var(--primary-purple);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.attachButton {
  background: none;
  border: none;
  color: var(--text-tertiary);
  padding: var(--space-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.attachButton:hover {
  color: var(--text-secondary);
  background: var(--bg-secondary);
}

.messageInput {
  flex: 1;
  background: none;
  border: none;
  outline: none;
  color: var(--text-primary);
  font-size: 15px;
  font-family: var(--font-family);
  line-height: 1.5;
}

.messageInput::placeholder {
  color: var(--text-tertiary);
}

.inputActions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* Voice Button */
.voiceButton {
  position: relative;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.voiceButton:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.voiceButton.listening {
  background: var(--primary-purple);
  color: var(--text-white);
}

.voiceButton.denied {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-red);
}

.listeningIndicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voicePulse {
  position: absolute;
  inset: -4px;
  border: 2px solid currentColor;
  border-radius: var(--radius-lg);
  animation: voicePulse 2s infinite;
  opacity: 0.6;
}

@keyframes voicePulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

/* Send Button */
.sendButton {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: var(--radius-lg);
  background: var(--primary-purple);
  color: var(--text-white);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sendButton:hover:not(.disabled) {
  background: var(--primary-purple-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-purple);
}

.sendButton.disabled {
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  cursor: not-allowed;
  transform: none;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .appWrapper {
    padding: var(--space-4);
  }

  .chatContainer {
    height: calc(100vh - 2rem);
    border-radius: var(--radius-2xl);
  }

  .header {
    padding: var(--space-4);
  }

  .headerTitle {
    font-size: 18px;
  }

  .messagesContent {
    padding: var(--space-4);
  }

  .welcomeState {
    padding: var(--space-8) var(--space-4);
  }

  .welcomeTitle {
    font-size: 20px;
  }

  .featureCards {
    grid-template-columns: 1fr;
    max-width: 300px;
  }

  .messageCard {
    max-width: 85%;
  }

  .inputSection {
    padding: var(--space-4);
  }
}

@media (max-width: 480px) {
  .appWrapper {
    padding: var(--space-2);
  }

  .chatContainer {
    height: calc(100vh - 1rem);
    border-radius: var(--radius-xl);
  }

  .headerContent {
    gap: var(--space-3);
  }

  .aiAvatar {
    width: 40px;
    height: 40px;
  }

  .headerTitle {
    font-size: 16px;
  }

  .headerSubtitle {
    font-size: 13px;
  }

  .messageWrapper {
    margin-bottom: var(--space-4);
  }

  .messageCard {
    max-width: 90%;
  }

  .messageContent {
    padding: var(--space-4);
  }

  .messageText {
    font-size: 14px;
  }

  .speechControlsCard {
    margin: var(--space-3);
    padding: var(--space-3);
    flex-direction: column;
    gap: var(--space-3);
    align-items: stretch;
  }

  .speechActions {
    justify-content: center;
  }

  .inputContainer {
    padding: var(--space-2);
    gap: var(--space-2);
  }

  .messageInput {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .voiceButton,
  .sendButton {
    width: 32px;
    height: 32px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-light: #94a3b8;
    --border-medium: #64748b;
    --text-tertiary: #475569;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
