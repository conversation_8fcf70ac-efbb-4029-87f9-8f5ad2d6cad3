import { ProviderRepository } from "../repositories/provider.repository.js";

export const providerTool = {
  name: "providerInfo",
  description: "Get information about a provider by name.",
  /**
   * @param {Object} input
   * @param {string} input.providerName - The name of the provider to look up.
   * @returns {Promise<Object>} Provider information or error.
   */
  async call({ providerName }) {
    console.log("[AGENT] providerTool is handling the request.");
    if (!providerName) {
      return { error: "providerName is required" };
    }
    const result = await ProviderRepository.findProviderByName(providerName);
    return result;
  },
};
