import * as chatMemory from "./chat-memory.service.js";
import { LLMService } from "./llm.service.js";
import { ProviderRepository } from "../repositories/provider.repository.js";

/**
 * Full provider query processing pipeline: classify, fetch, and generate response.
 * @param {Object} params
 * @param {string} params.message - The user's message.
 * @param {Object} params.entities - Extracted entities.
 * @param {string} [params.intent] - Detected intent
 * @param {string} [params.query] - Refined query
 * @param {string} [params.queryType] - Detected query type
 * @param {object} [params.filters] - Detected filters
 * @param {string} [params.userId] - User ID
 * @param {string} [params.sessionId] - Session ID
 * @returns {Promise<{ providers: any, message: string }>}
 */
export async function processProviderQuery({
  message,
  entities,
  intent,
  query,
  queryType,
  filters,
  userId,
  sessionId,
}) {
  console.log("processProviderQuery");
  console.log("message", message);
  console.log("entities", entities);
  console.log("intent", intent);
  console.log("query", query);
  console.log("queryType", queryType);
  console.log("filters", filters);
  // Pass entities directly
  const detection = { queryType, filters, entities };
  const providerData = await handleProviderQueryAction(detection);

  // Use generateProviderResponse for response generation, now with sessionId
  const { message: enhancedMessage, data } = await generateProviderResponse({
    userMessage: message,
    entities,
    providerData,
    sessionId,
  });

  return {
    message: enhancedMessage,
    data,
  };
}

/**
 * Calls the appropriate ProviderRepository function based on the detected query type.
 * @param {Object} params
 * @param {string} params.queryType - The classified query type
 * @param {string|null} params.serviceType
 * @param {string|null} params.providerName
 * @param {object|null} params.filters
 * @returns {Promise<any>} The result of the repository action or an error object.
 */
export async function handleProviderQueryAction({
  queryType,
  filters,
  entities,
}) {
  const providerName = entities?.providerName ?? null;
  switch (queryType) {
    case "general":
      // Pass limit to findAvailableProviders if present
      return await ProviderRepository.findAvailableProviders(
        filters && filters.limit ? filters.limit : undefined
      );
    case "detailed":
      if (providerName) {
        return await ProviderRepository.findProviderByName(providerName);
      }
      return { error: "No providerName detected for detailed query." };
    case "filtered": {
      const mergedFilters = { ...entities, ...filters };
      return await ProviderRepository.findProvidersWithFilters(mergedFilters);
    }
    default:
      return { error: "Could not determine provider query type." };
  }
}

/**
 * Generates a user-friendly provider response using the LLM.
 * @param {Object} params
 * @param {string} params.userMessage - The user's original message.
 * @param {Object} params.entities - Extracted entities.
 * @param {any} params.providerData - The raw provider data from the repository.
 * @param {string} [params.sessionId] - Session ID for memory integration.
 * @returns {Promise<{ message: string, data: any }>} The message and provider data.
 */
export async function generateProviderResponse({
  userMessage,
  entities,
  providerData,
  sessionId,
}) {
  let history = "";
  if (sessionId) {
    await chatMemory.appendToHistory(sessionId, "USER", userMessage);
    history = await chatMemory.getFormattedHistory(sessionId);
  }

  // Use history as context for LLM response
  const enhancedMessage = await LLMService.generateStructuredResponse(
    `Conversation so far: ${history}\nUser asked: "${userMessage}". Here are the results: ${JSON.stringify(
      providerData
    )}`,
    "provider_list"
  );

  if (sessionId) {
    await chatMemory.appendToHistory(sessionId, "ASSISTANT", enhancedMessage);
  }
  return {
    message: enhancedMessage,
    data: providerData,
  };
}
