import { serviceRequestPrompts } from "../prompts/service-request.prompt.js";
import { LLMService } from "./llm.service.js";
import { serviceRequestLLMFlowPrompt } from "../prompts/service-request-llm-flow.prompt.js";
import * as chatMemory from "./chat-memory.service.js";
import { saveServiceRequest } from "../repositories/service-request.repository.js";
import { serviceRequestExtractPrompt } from "../prompts/service-request-extract.prompt.js";
import {
  serviceRequestConfirmationPrompt,
  userConfirmationPrompt,
} from "../prompts/service-request-confirmation.prompt.js";

// const userStates = {}; // In-memory; replace with DB/session for production
// const stepInstructions = [
//   "Ask the user for their car details (make, model, year) in a friendly, conversational way.",
//   "Ask the user for their preferred date and time for the service in a friendly, conversational way.",
//   "Ask the user for their full service address and type in a friendly, conversational way.",
//   "Ask the user if they have a preferred technician or if any available is fine, in a friendly, conversational way.",
//   "Ask the user if they have any special instructions for the service in a friendly, conversational way."
// ];
const llmUserStates = {}; // In-memory for LLM-driven flow

export class ServiceRequestService {
  // static steps = ["car", "appointment", "address", "provider", "instructions"];
  // static prompts = [
  //   serviceRequestPrompts.askCar,
  //   serviceRequestPrompts.askAppointment,
  //   serviceRequestPrompts.askAddress,
  //   serviceRequestPrompts.askProvider,
  //   serviceRequestPrompts.askInstructions,
  // ];

  // static async handle(userId, message) {
  //   if (!userStates[userId]) {
  //     userStates[userId] = { step: 0, data: {} };
  //   }
  //   const state = userStates[userId];
  //   // Save previous answer
  //   if (state.step > 0) {
  //     const prevKey = ServiceRequestService.steps[state.step - 1];
  //     state.data[prevKey] = message;
  //   }
  //   // If all steps done, confirm and clear state
  //   if (state.step >= ServiceRequestService.steps.length) {
  //     // TODO: Create the actual service request here
  //     const summary = JSON.stringify(state.data, null, 2);
  //     delete userStates[userId];
  //     return { message: `${serviceRequestPrompts.confirm}\n${summary}` };
  //   }
  //   // Ask next question using LLM for all steps
  //   const instruction = stepInstructions[state.step];
  //   const prompt = await LLMService.generateResponse(instruction);
  //   state.step += 1;
  //   return { message: prompt };
  // }

  // LLM-driven, prompt-only flow with chat memory and DB save
  static async handleLLMFlow({ userId, message, sessionId }) {
    console.log(
      `[INFO] ServiceRequest handleLLMFlow - userId: ${userId}, sessionId: ${sessionId}`
    );
    console.log(`[INFO] User message: ${message}`);

    // Always retrieve collected data internally
    const collected = ServiceRequestService.getCollected(userId);
    console.log(
      `[INFO] Current collected data:`,
      JSON.stringify(collected, null, 2)
    );

    // Append user message to chat memory
    if (sessionId) {
      await chatMemory.appendToHistory(sessionId, "USER", message);
    }

    // Get formatted history from chat memory
    const history = sessionId
      ? await chatMemory.getFormattedHistory(sessionId)
      : "";

    // Extract and update collected data from user message and history
    const updatedCollected = await ServiceRequestService.extractAndUpdateData(
      message,
      history,
      collected,
      userId
    );
    console.log(
      `[INFO] Updated collected data:`,
      JSON.stringify(updatedCollected, null, 2)
    );

    const prompt = serviceRequestLLMFlowPrompt
      .replace("{collected}", JSON.stringify(updatedCollected, null, 2))
      .replace("{history}", history);

    const response = await LLMService.generateResponse(prompt);
    console.log(`[INFO] LLM response: ${response}`);

    // Append assistant response to chat memory
    if (sessionId) {
      await chatMemory.appendToHistory(sessionId, "ASSISTANT", response);
    }

    // Check if all required details are collected and user confirmed
    const allDetailsCollected =
      ServiceRequestService.isAllDetailsCollected(updatedCollected);
    const isConfirmation =
      (await ServiceRequestService.isConfirmationMessage(response)) ||
      (await ServiceRequestService.isUserConfirmation(message));

    console.log(`[INFO] All details collected: ${allDetailsCollected}`);
    console.log(`[INFO] Is confirmation message: ${isConfirmation}`);
    console.log(`[INFO] Required fields check:`, {
      vehicle: !!updatedCollected.vehicle,
      appointment: !!updatedCollected.appointment,
      location: !!updatedCollected.location,
      provider: !!updatedCollected.provider,
      instructions: !!updatedCollected.instructions,
    });

    console.log(`[DEBUG] Save conditions check:`);
    console.log(`[DEBUG] - allDetailsCollected: ${allDetailsCollected}`);
    console.log(`[DEBUG] - isConfirmation: ${isConfirmation}`);
    console.log(
      `[DEBUG] - Should save: ${allDetailsCollected && isConfirmation}`
    );

    if (allDetailsCollected && isConfirmation) {
      console.log(`\n🔥🔥🔥 SAVING TO DATABASE 🔥🔥🔥`);
      console.log(
        `[INFO] ✅ ATTEMPTING TO SAVE SERVICE REQUEST TO DATABASE...`
      );
      console.log(`[DEBUG] User ID: ${userId} (type: ${typeof userId})`);
      console.log(`[DEBUG] Session ID: ${sessionId}`);
      console.log(`[DEBUG] All details collected: ${allDetailsCollected}`);
      console.log(`[DEBUG] Is confirmation: ${isConfirmation}`);

      // Create a clean JSON structure for database saving
      const dataForDatabase = {
        userId: userId,
        vehicle: updatedCollected.vehicle || {},
        appointment: updatedCollected.appointment || {},
        location: updatedCollected.location || {},
        provider: updatedCollected.provider || {},
        instructions: updatedCollected.instructions || {},
        serviceType: updatedCollected.serviceType || "brake repair",
        timestamp: new Date().toISOString(),
      };

      console.log(`[DEBUG] 📋 CLEAN DATA STRUCTURE FOR DATABASE:`);
      console.log(JSON.stringify(dataForDatabase, null, 2));

      try {
        console.log(`[INFO] 🚀 CALLING saveServiceRequest function...`);
        const saveResult = await saveServiceRequest(updatedCollected, userId);
        console.log(`[SUCCESS] 🎉 SERVICE REQUEST SAVED SUCCESSFULLY!`);
        console.log(
          `[SUCCESS] 📊 SAVE RESULT:`,
          JSON.stringify(saveResult, null, 2)
        );

        // Clear collected state
        ServiceRequestService.setCollected(userId, {});
        console.log(`[INFO] Cleared collected state for user ${userId}`);

        return {
          message: `✅ Your service request has been created and booked successfully!`,
        };
      } catch (error) {
        console.error(`[ERROR] Failed to save service request:`, error);
        return {
          message:
            "I apologize, but there was an error saving your service request. Please try again.",
          data: null,
        };
      }
    } else {
      console.log(`\n❌ NOT SAVING TO DATABASE - CONDITIONS NOT MET:`);
      console.log(`[DEBUG] - allDetailsCollected: ${allDetailsCollected}`);
      console.log(`[DEBUG] - isConfirmation: ${isConfirmation}`);
      console.log(
        `[DEBUG] - Both required: ${allDetailsCollected && isConfirmation}`
      );

      if (!allDetailsCollected) {
        console.log(
          `[INFO] 📝 Still collecting data. Missing fields detected.`
        );
      }
      if (!isConfirmation) {
        console.log(
          `[INFO] 🤔 Not a confirmation message. Waiting for user confirmation.`
        );
      }
    }

    return { message: response };
  }

  // Get collected data for a user/session
  static getCollected(userId) {
    if (!llmUserStates[userId]) {
      llmUserStates[userId] = { collected: {} };
    }
    return llmUserStates[userId].collected;
  }

  // Set collected data for a user/session
  static setCollected(userId, collected) {
    if (!llmUserStates[userId]) {
      llmUserStates[userId] = { collected: {} };
    }
    llmUserStates[userId].collected = collected;
  }

  // Helper: Check if all required details are present
  static isAllDetailsCollected(collected) {
    // Ensure collected is an object
    if (!collected || typeof collected !== "object") {
      return false;
    }

    // Check if all required fields are present and have data
    const hasVehicle =
      collected.vehicle && Object.keys(collected.vehicle).length > 0;
    const hasAppointment =
      collected.appointment && Object.keys(collected.appointment).length > 0;
    const hasLocation =
      collected.location && Object.keys(collected.location).length > 0;
    const hasProvider =
      collected.provider && Object.keys(collected.provider).length > 0;
    const hasInstructions =
      collected.instructions && typeof collected.instructions === "object";

    console.log(`[INFO] Detailed field check:`, {
      hasVehicle,
      hasAppointment,
      hasLocation,
      hasProvider,
      hasInstructions,
    });

    // Provider is optional - if not specified, we'll auto-assign one during save
    // Instructions are also optional
    const result = hasVehicle && hasAppointment && hasLocation;

    console.log(`[DEBUG] isAllDetailsCollected result: ${result}`);
    console.log(
      `[DEBUG] Required fields: vehicle=${hasVehicle}, appointment=${hasAppointment}, location=${hasLocation}, provider=${hasProvider} (optional)`
    );

    return result;
  }

  // Helper: Check if the LLM response is a confirmation (improved detection)
  static async isConfirmationMessage(response) {
    const prompt = serviceRequestConfirmationPrompt.format({
      message: response,
    });
    try {
      const llmResponse = await LLMService.generateResponse(prompt);
      return llmResponse.trim().toLowerCase().startsWith("true");
    } catch (e) {
      console.error(
        "[ERROR] LLM confirmation check failed, falling back to pattern match.",
        e
      );
      // fallback to old logic
      const confirmationPatterns = [
        /confirm|summary|all set|created|booked|scheduled|done|success/i,
        /would you like to confirm/i,
        /here is a summary/i,
        /service request.*summary/i,
        /would you like to.*book/i,
        /confirm and book/i,
      ];
      return confirmationPatterns.some((pattern) => pattern.test(response));
    }
  }

  // Helper: Check if the user's message is a confirmation
  static async isUserConfirmation(userMessage) {
    const prompt = userConfirmationPrompt.format({ message: userMessage });
    try {
      const llmResponse = await LLMService.generateResponse(prompt);
      return llmResponse.trim().toLowerCase().startsWith("true");
    } catch (e) {
      console.error(
        "[ERROR] LLM user confirmation check failed, falling back to pattern match.",
        e
      );
      // fallback to old logic
      const userConfirmationPatterns = [
        /^yes\b/i,
        /^yeah\b/i,
        /^yep\b/i,
        /^sure\b/i,
        /^ok\b/i,
        /^okay\b/i,
        /book it/i,
        /confirm/i,
        /go ahead/i,
        /proceed/i,
        /yes.*book/i,
        /yes.*confirm/i,
        /book.*service/i,
        /schedule.*it/i,
      ];
      return userConfirmationPatterns.some((pattern) =>
        pattern.test(userMessage.trim())
      );
    }
  }

  // Extract and update service request data from user message and conversation history
  static async extractAndUpdateData(
    message,
    history,
    currentCollected,
    userId
  ) {
    console.log(`[INFO] Extracting data from message using LLM: ${message}`);
    console.log(
      `[DEBUG] Current collected before extraction:`,
      JSON.stringify(currentCollected, null, 2)
    );

    const prompt = serviceRequestExtractPrompt.format({ history, message });
    console.log(`[DEBUG] Extraction prompt:`, prompt);

    let llmResponse;
    try {
      llmResponse = await LLMService.generateResponse(prompt);
      console.log(`[DEBUG] Raw LLM response:`, llmResponse);
    } catch (e) {
      console.error("[ERROR] LLM extraction failed:", e);
      return currentCollected;
    }

    let extracted;
    try {
      // Clean the response - remove any markdown formatting or extra text
      let cleanResponse = llmResponse.trim();

      // Remove markdown code blocks if present
      cleanResponse = cleanResponse
        .replace(/```json\s*/g, "")
        .replace(/```\s*/g, "");

      // Try to extract the first JSON object from the response
      const match = cleanResponse.match(/\{[\s\S]*\}/);
      if (!match) {
        console.error(
          "[ERROR] No JSON object found in LLM response:",
          cleanResponse
        );
        throw new Error("No JSON object found in LLM response");
      }

      console.log(`[DEBUG] Extracted JSON string:`, match[0]);
      extracted = JSON.parse(match[0]);
      console.log(
        `[DEBUG] Parsed extracted data:`,
        JSON.stringify(extracted, null, 2)
      );
    } catch (e) {
      console.error("[ERROR] Failed to parse LLM JSON response:", e);
      console.error("[ERROR] Raw response was:", llmResponse);

      // Fallback: try to extract vehicle data manually from the message
      console.log("[INFO] Attempting manual extraction as fallback...");
      extracted = ServiceRequestService.manualExtractVehicleData(message);
      console.log(
        `[DEBUG] Manual extraction result:`,
        JSON.stringify(extracted, null, 2)
      );
    }

    // Merge with currentCollected to preserve previous data
    const updated = { ...currentCollected };

    // Only merge non-null values from extracted data
    Object.keys(extracted).forEach((key) => {
      if (extracted[key] !== null && extracted[key] !== undefined) {
        if (typeof extracted[key] === "object" && extracted[key] !== null) {
          // For objects, merge non-null properties
          updated[key] = { ...(updated[key] || {}), ...extracted[key] };
          // Remove null values from the merged object
          Object.keys(updated[key]).forEach((subKey) => {
            if (updated[key][subKey] === null) {
              delete updated[key][subKey];
            }
          });
        } else {
          updated[key] = extracted[key];
        }
      }
    });

    ServiceRequestService.setCollected(userId, updated);
    console.log(`[INFO] Saved updated collected data for user ${userId}`);
    console.log(
      `[DEBUG] Final updated data:`,
      JSON.stringify(updated, null, 2)
    );
    return updated;
  }

  // Manual fallback extraction for vehicle data
  static manualExtractVehicleData(message) {
    console.log(`[INFO] Manual extraction from: ${message}`);

    const extracted = {
      vehicle: null,
      appointment: null,
      location: null,
      provider: null,
      instructions: null,
    };

    // Try to extract vehicle information using regex patterns
    const vehiclePatterns = {
      // Pattern for "Chevrolet Pilot 2020 Limited, 3.5L V6, CVT"
      full: /(\w+)\s+(\w+)\s+(\d{4})\s+(\w+),?\s*([\d.]+L\s*\w+),?\s*(\w+)/i,
      // Pattern for "Make Model Year"
      basic: /(\w+)\s+(\w+)\s+(\d{4})/i,
      // Pattern for year
      year: /\b(19|20)\d{2}\b/,
      // Pattern for engine
      engine: /\b\d+\.?\d*L?\s*(V\d+|I\d+|\w+)\b/i,
      // Pattern for transmission
      transmission: /\b(CVT|Manual|Automatic|Auto|MT|AT)\b/i,
    };

    try {
      const fullMatch = message.match(vehiclePatterns.full);
      if (fullMatch) {
        extracted.vehicle = {
          make: fullMatch[1],
          model: fullMatch[2],
          year: fullMatch[3],
          trim: fullMatch[4],
          engine: fullMatch[5],
          transmission: fullMatch[6],
        };
        console.log(
          `[SUCCESS] Full vehicle pattern matched:`,
          extracted.vehicle
        );
      } else {
        // Try basic pattern
        const basicMatch = message.match(vehiclePatterns.basic);
        if (basicMatch) {
          extracted.vehicle = {
            make: basicMatch[1],
            model: basicMatch[2],
            year: basicMatch[3],
          };

          // Try to extract additional details
          const engineMatch = message.match(vehiclePatterns.engine);
          if (engineMatch) {
            extracted.vehicle.engine = engineMatch[0];
          }

          const transmissionMatch = message.match(vehiclePatterns.transmission);
          if (transmissionMatch) {
            extracted.vehicle.transmission = transmissionMatch[0];
          }

          console.log(
            `[SUCCESS] Basic vehicle pattern matched:`,
            extracted.vehicle
          );
        } else {
          console.log(
            `[WARNING] No vehicle pattern matched in message: ${message}`
          );
        }
      }
    } catch (error) {
      console.error(`[ERROR] Manual extraction failed:`, error);
    }

    return extracted;
  }
}
