import { serviceRequestPrompts } from "../prompts/service-request.prompt.js";
import { LLMService } from "./llm.service.js";
import { serviceRequestLLMFlowPrompt } from "../prompts/service-request-llm-flow.prompt.js";
import * as chatMemory from "./chat-memory.service.js";
import { saveServiceRequest } from "../repositories/service-request.repository.js";
import { serviceRequestExtractPrompt } from "../prompts/service-request-extract.prompt.js";
import { serviceRequestConfirmationPrompt } from "../prompts/service-request-confirmation.prompt.js";
import { VehicleRepository } from "../repositories/vehicle.repository.js";
import { AddressRepository } from "../repositories/address.repository.js";

// const userStates = {}; // In-memory; replace with DB/session for production
// const stepInstructions = [
//   "Ask the user for their car details (make, model, year) in a friendly, conversational way.",
//   "Ask the user for their preferred date and time for the service in a friendly, conversational way.",
//   "Ask the user for their full service address and type in a friendly, conversational way.",
//   "Ask the user if they have a preferred technician or if any available is fine, in a friendly, conversational way.",
//   "Ask the user if they have any special instructions for the service in a friendly, conversational way."
// ];
const llmUserStates = {}; // In-memory for LLM-driven flow

export class ServiceRequestService {
  // static steps = ["car", "appointment", "address", "provider", "instructions"];
  // static prompts = [
  //   serviceRequestPrompts.askCar,
  //   serviceRequestPrompts.askAppointment,
  //   serviceRequestPrompts.askAddress,
  //   serviceRequestPrompts.askProvider,
  //   serviceRequestPrompts.askInstructions,
  // ];

  // static async handle(userId, message) {
  //   if (!userStates[userId]) {
  //     userStates[userId] = { step: 0, data: {} };
  //   }
  //   const state = userStates[userId];
  //   // Save previous answer
  //   if (state.step > 0) {
  //     const prevKey = ServiceRequestService.steps[state.step - 1];
  //     state.data[prevKey] = message;
  //   }
  //   // If all steps done, confirm and clear state
  //   if (state.step >= ServiceRequestService.steps.length) {
  //     // TODO: Create the actual service request here
  //     const summary = JSON.stringify(state.data, null, 2);
  //     delete userStates[userId];
  //     return { message: `${serviceRequestPrompts.confirm}\n${summary}` };
  //   }
  //   // Ask next question using LLM for all steps
  //   const instruction = stepInstructions[state.step];
  //   const prompt = await LLMService.generateResponse(instruction);
  //   state.step += 1;
  //   return { message: prompt };
  // }

  // LLM-driven, prompt-only flow with chat memory and DB save
  static async handleLLMFlow({ userId, message, sessionId }) {
    console.log(
      `[INFO] ServiceRequest handleLLMFlow - userId: ${userId}, sessionId: ${sessionId}`
    );
    console.log(`[INFO] User message: ${message}`);

    // Always retrieve collected data internally
    const collected = ServiceRequestService.getCollected(userId);
    console.log(
      `[INFO] Current collected data:`,
      JSON.stringify(collected, null, 2)
    );

    // Append user message to chat memory
    if (sessionId) {
      await chatMemory.appendToHistory(sessionId, "USER", message);
    }

    // Get formatted history from chat memory
    const history = sessionId
      ? await chatMemory.getFormattedHistory(sessionId)
      : "";

    // Extract and update collected data from user message and history
    const updatedCollected = await ServiceRequestService.extractAndUpdateData(
      message,
      history,
      collected,
      userId
    );
    console.log(
      `[INFO] Updated collected data:`,
      JSON.stringify(updatedCollected, null, 2)
    );

    // Validate extracted data against database
    const validationResult = await ServiceRequestService.validateExtractedData(
      updatedCollected,
      userId
    );
    console.log(
      `[INFO] Validation result:`,
      JSON.stringify(validationResult, null, 2)
    );

    // Note: Service request tool is now handled by LangChain agent
    // The agent will automatically choose the right tool based on user intent

    // Check if user is confirming to add missing data (vehicle/address)
    const isConfirmingAddition =
      await ServiceRequestService.isUserConfirmingAddition(
        message,
        validationResult
      );

    if (isConfirmingAddition && !validationResult.isValid) {
      console.log(`[INFO] User confirmed adding missing data to database`);

      // Add missing vehicle to database
      if (
        validationResult.vehicleValidation &&
        !validationResult.vehicleValidation.exists
      ) {
        try {
          const newVehicle = await VehicleRepository.createVehicle(
            validationResult.vehicleValidation.data,
            userId
          );
          console.log(`[SUCCESS] Added vehicle to database:`, newVehicle.id);

          // Update collected data with the new vehicle and mark as confirmed
          updatedCollected.vehicle = validationResult.vehicleValidation.data;
          updatedCollected.vehicle._confirmed = true; // Flag to avoid re-validation
          ServiceRequestService.setCollected(userId, updatedCollected);
        } catch (error) {
          console.error(`[ERROR] Failed to add vehicle:`, error);
          return {
            message:
              "Sorry, I couldn't add the vehicle to your garage. Please try again.",
          };
        }
      }

      // Add missing address to database
      if (
        validationResult.addressValidation &&
        !validationResult.addressValidation.exists
      ) {
        try {
          const newAddress = await AddressRepository.createAddress(
            validationResult.addressValidation.data,
            userId
          );
          console.log(`[SUCCESS] Added address to database:`, newAddress.id);

          // Update collected data with the new address and mark as confirmed
          updatedCollected.location = validationResult.addressValidation.data;
          updatedCollected.location._confirmed = true; // Flag to avoid re-validation
          ServiceRequestService.setCollected(userId, updatedCollected);
        } catch (error) {
          console.error(`[ERROR] Failed to add address:`, error);
          return {
            message:
              "Sorry, I couldn't add the address to your saved locations. Please try again.",
          };
        }
      }

      // Continue with the flow - ask for next missing information
      const nextPrompt = await ServiceRequestService.generateNextStepPrompt(
        updatedCollected
      );

      // Append response to chat memory
      if (sessionId) {
        await chatMemory.appendToHistory(sessionId, "ASSISTANT", nextPrompt);
      }

      return { message: nextPrompt };
    }

    // If validation found issues and user hasn't confirmed yet, ask for confirmation
    if (!validationResult.isValid) {
      const validationResponse =
        await ServiceRequestService.handleValidationIssues(
          validationResult,
          updatedCollected
        );

      // Append validation response to chat memory
      if (sessionId) {
        await chatMemory.appendToHistory(
          sessionId,
          "ASSISTANT",
          validationResponse
        );
      }

      return { message: validationResponse };
    }

    const prompt = serviceRequestLLMFlowPrompt
      .replace("{collected}", JSON.stringify(updatedCollected, null, 2))
      .replace("{history}", history);

    const response = await LLMService.generateResponse(prompt);
    console.log(`[INFO] LLM response: ${response}`);

    // Append assistant response to chat memory
    if (sessionId) {
      await chatMemory.appendToHistory(sessionId, "ASSISTANT", response);
    }

    // Check if all required details are collected and user confirmed
    const allDetailsCollected =
      ServiceRequestService.isAllDetailsCollected(updatedCollected);
    const isConfirmation =
      (await ServiceRequestService.isConfirmationMessage(response)) ||
      (await ServiceRequestService.isUserConfirmation(message));

    console.log(`[INFO] All details collected: ${allDetailsCollected}`);
    console.log(`[INFO] Is confirmation message: ${isConfirmation}`);
    console.log(`[INFO] Required fields check:`, {
      vehicle: !!updatedCollected.vehicle,
      appointment: !!updatedCollected.appointment,
      location: !!updatedCollected.location,
      provider: !!updatedCollected.provider,
      instructions: !!updatedCollected.instructions,
    });

    console.log(`[DEBUG] Save conditions check:`);
    console.log(`[DEBUG] - allDetailsCollected: ${allDetailsCollected}`);
    console.log(`[DEBUG] - isConfirmation: ${isConfirmation}`);
    console.log(
      `[DEBUG] - Should save: ${allDetailsCollected && isConfirmation}`
    );

    if (allDetailsCollected && isConfirmation) {
      console.log(`\n🔥🔥🔥 SAVING TO DATABASE 🔥🔥🔥`);
      console.log(
        `[INFO] ✅ ATTEMPTING TO SAVE SERVICE REQUEST TO DATABASE...`
      );
      console.log(`[DEBUG] User ID: ${userId} (type: ${typeof userId})`);
      console.log(`[DEBUG] Session ID: ${sessionId}`);
      console.log(`[DEBUG] All details collected: ${allDetailsCollected}`);
      console.log(`[DEBUG] Is confirmation: ${isConfirmation}`);

      // Create a clean JSON structure for database saving
      const dataForDatabase = {
        userId: userId,
        vehicle: updatedCollected.vehicle || {},
        appointment: updatedCollected.appointment || {},
        location: updatedCollected.location || {},
        provider: updatedCollected.provider || {},
        instructions: updatedCollected.instructions || {},
        serviceType: updatedCollected.serviceType || "brake repair",
        timestamp: new Date().toISOString(),
      };

      console.log(`[DEBUG] 📋 CLEAN DATA STRUCTURE FOR DATABASE:`);
      console.log(JSON.stringify(dataForDatabase, null, 2));

      try {
        console.log(`[INFO] 🚀 CALLING saveServiceRequest function...`);
        const saveResult = await saveServiceRequest(updatedCollected, userId);
        console.log(`[SUCCESS] 🎉 SERVICE REQUEST SAVED SUCCESSFULLY!`);
        console.log(
          `[SUCCESS] 📊 SAVE RESULT:`,
          JSON.stringify(saveResult, null, 2)
        );

        // Clear collected state
        ServiceRequestService.setCollected(userId, {});
        console.log(`[INFO] Cleared collected state for user ${userId}`);

        return {
          message: `✅ Your service request has been created and booked successfully!`,
        };
      } catch (error) {
        console.error(`[ERROR] Failed to save service request:`, error);
        return {
          message:
            "I apologize, but there was an error saving your service request. Please try again.",
          data: null,
        };
      }
    } else {
      console.log(`\n❌ NOT SAVING TO DATABASE - CONDITIONS NOT MET:`);
      console.log(`[DEBUG] - allDetailsCollected: ${allDetailsCollected}`);
      console.log(`[DEBUG] - isConfirmation: ${isConfirmation}`);
      console.log(
        `[DEBUG] - Both required: ${allDetailsCollected && isConfirmation}`
      );

      if (!allDetailsCollected) {
        console.log(
          `[INFO] 📝 Still collecting data. Missing fields detected.`
        );
      }
      if (!isConfirmation) {
        console.log(
          `[INFO] 🤔 Not a confirmation message. Waiting for user confirmation.`
        );
      }
    }

    return { message: response };
  }

  // Get collected data for a user/session
  static getCollected(userId) {
    if (!llmUserStates[userId]) {
      llmUserStates[userId] = { collected: {} };
    }
    return llmUserStates[userId].collected;
  }

  // Set collected data for a user/session
  static setCollected(userId, collected) {
    if (!llmUserStates[userId]) {
      llmUserStates[userId] = { collected: {} };
    }
    llmUserStates[userId].collected = collected;
  }

  // Helper: Check if all required details are present
  static isAllDetailsCollected(collected) {
    // Ensure collected is an object
    if (!collected || typeof collected !== "object") {
      return false;
    }

    // Check if all required fields are present and have data
    const hasVehicle =
      collected.vehicle && Object.keys(collected.vehicle).length > 0;
    const hasAppointment =
      collected.appointment && Object.keys(collected.appointment).length > 0;
    const hasLocation =
      collected.location && Object.keys(collected.location).length > 0;
    const hasProvider =
      collected.provider && Object.keys(collected.provider).length > 0;
    const hasInstructions =
      collected.instructions && typeof collected.instructions === "object";

    console.log(`[INFO] Detailed field check:`, {
      hasVehicle,
      hasAppointment,
      hasLocation,
      hasProvider,
      hasInstructions,
    });

    // Provider is optional - if not specified, we'll auto-assign one during save
    // Instructions are also optional
    const result = hasVehicle && hasAppointment && hasLocation;

    console.log(`[DEBUG] isAllDetailsCollected result: ${result}`);
    console.log(
      `[DEBUG] Required fields: vehicle=${hasVehicle}, appointment=${hasAppointment}, location=${hasLocation}, provider=${hasProvider} (optional)`
    );

    return result;
  }

  // Helper: Check if the LLM response is a confirmation (improved detection)
  static async isConfirmationMessage(response) {
    const prompt = serviceRequestConfirmationPrompt.format({
      message: response,
    });
    try {
      const llmResponse = await LLMService.generateResponse(prompt);
      return llmResponse.trim().toLowerCase().startsWith("true");
    } catch (e) {
      console.error(
        "[ERROR] LLM confirmation check failed, falling back to pattern match.",
        e
      );
      // fallback to old logic
      const confirmationPatterns = [
        /confirm|summary|all set|created|booked|scheduled|done|success/i,
        /would you like to confirm/i,
        /here is a summary/i,
        /service request.*summary/i,
        /would you like to.*book/i,
        /confirm and book/i,
      ];
      return confirmationPatterns.some((pattern) => pattern.test(response));
    }
  }

  // Helper: Check if the user's message is a confirmation (optimized)
  static async isUserConfirmation(userMessage) {
    console.log(`[INFO] Checking confirmation for: ${userMessage}`);

    // Use fast regex patterns first (no LLM call needed)
    const userConfirmationPatterns = [
      /^yes\b/i,
      /^yeah\b/i,
      /^yep\b/i,
      /^sure\b/i,
      /^ok\b/i,
      /^okay\b/i,
      /book it/i,
      /confirm/i,
      /go ahead/i,
      /proceed/i,
      /yes.*book/i,
      /yes.*confirm/i,
      /book.*service/i,
      /schedule.*it/i,
      /please.*book/i,
      /book.*this/i,
    ];

    const isConfirming = userConfirmationPatterns.some((pattern) =>
      pattern.test(userMessage.trim())
    );

    console.log(`[DEBUG] Confirmation result: ${isConfirming}`);
    return isConfirming;
  }

  // Extract and update service request data from user message and conversation history
  static async extractAndUpdateData(
    message,
    history,
    currentCollected,
    userId
  ) {
    console.log(`[INFO] Extracting data from message using LLM: ${message}`);
    console.log(
      `[DEBUG] Current collected before extraction:`,
      JSON.stringify(currentCollected, null, 2)
    );

    const prompt = await serviceRequestExtractPrompt.format({
      history,
      message,
    });
    console.log(`[DEBUG] Extraction prompt:`, prompt);

    let llmResponse;
    try {
      llmResponse = await LLMService.generateResponse(prompt);
      console.log(`[DEBUG] Raw LLM response:`, llmResponse);
    } catch (e) {
      console.error("[ERROR] LLM extraction failed:", e);
      return currentCollected;
    }

    let extracted;
    try {
      // Clean the response - remove any markdown formatting or extra text
      let cleanResponse = llmResponse.trim();

      // Remove markdown code blocks if present
      cleanResponse = cleanResponse
        .replace(/```json\s*/g, "")
        .replace(/```\s*/g, "");

      // Try to extract the first JSON object from the response
      const match = cleanResponse.match(/\{[\s\S]*\}/);
      if (!match) {
        console.error(
          "[ERROR] No JSON object found in LLM response:",
          cleanResponse
        );
        throw new Error("No JSON object found in LLM response");
      }

      console.log(`[DEBUG] Extracted JSON string:`, match[0]);
      extracted = JSON.parse(match[0]);
      console.log(
        `[DEBUG] Parsed extracted data:`,
        JSON.stringify(extracted, null, 2)
      );
    } catch (e) {
      console.error("[ERROR] Failed to parse LLM JSON response:", e);
      console.error("[ERROR] Raw response was:", llmResponse);

      // Fallback: try to extract vehicle data manually from the message
      console.log("[INFO] Attempting manual extraction as fallback...");
      extracted = ServiceRequestService.manualExtractVehicleData(message);
      console.log(
        `[DEBUG] Manual extraction result:`,
        JSON.stringify(extracted, null, 2)
      );
    }

    // Merge with currentCollected to preserve previous data
    const updated = { ...currentCollected };

    // Only merge non-null values from extracted data
    Object.keys(extracted).forEach((key) => {
      if (extracted[key] !== null && extracted[key] !== undefined) {
        if (typeof extracted[key] === "object" && extracted[key] !== null) {
          // For objects, merge non-null properties
          updated[key] = { ...(updated[key] || {}), ...extracted[key] };
          // Remove null values from the merged object
          Object.keys(updated[key]).forEach((subKey) => {
            if (updated[key][subKey] === null) {
              delete updated[key][subKey];
            }
          });
        } else {
          updated[key] = extracted[key];
        }
      }
    });

    ServiceRequestService.setCollected(userId, updated);
    console.log(`[INFO] Saved updated collected data for user ${userId}`);
    console.log(
      `[DEBUG] Final updated data:`,
      JSON.stringify(updated, null, 2)
    );
    return updated;
  }

  // Manual fallback extraction for vehicle data
  static manualExtractVehicleData(message) {
    console.log(`[INFO] Manual extraction from: ${message}`);

    const extracted = {
      vehicle: null,
      appointment: null,
      location: null,
      provider: null,
      instructions: null,
    };

    // Try to extract vehicle information using regex patterns
    const vehiclePatterns = {
      // Pattern for "Chevrolet Pilot 2020 Limited, 3.5L V6, CVT"
      full: /(\w+)\s+(\w+)\s+(\d{4})\s+(\w+),?\s*([\d.]+L\s*\w+),?\s*(\w+)/i,
      // Pattern for "Make Model Year"
      basic: /(\w+)\s+(\w+)\s+(\d{4})/i,
      // Pattern for year
      year: /\b(19|20)\d{2}\b/,
      // Pattern for engine
      engine: /\b\d+\.?\d*L?\s*(V\d+|I\d+|\w+)\b/i,
      // Pattern for transmission
      transmission: /\b(CVT|Manual|Automatic|Auto|MT|AT)\b/i,
    };

    try {
      const fullMatch = message.match(vehiclePatterns.full);
      if (fullMatch) {
        extracted.vehicle = {
          make: fullMatch[1],
          model: fullMatch[2],
          year: fullMatch[3],
          trim: fullMatch[4],
          engine: fullMatch[5],
          transmission: fullMatch[6],
        };
        console.log(
          `[SUCCESS] Full vehicle pattern matched:`,
          extracted.vehicle
        );
      } else {
        // Try basic pattern
        const basicMatch = message.match(vehiclePatterns.basic);
        if (basicMatch) {
          extracted.vehicle = {
            make: basicMatch[1],
            model: basicMatch[2],
            year: basicMatch[3],
          };

          // Try to extract additional details
          const engineMatch = message.match(vehiclePatterns.engine);
          if (engineMatch) {
            extracted.vehicle.engine = engineMatch[0];
          }

          const transmissionMatch = message.match(vehiclePatterns.transmission);
          if (transmissionMatch) {
            extracted.vehicle.transmission = transmissionMatch[0];
          }

          console.log(
            `[SUCCESS] Basic vehicle pattern matched:`,
            extracted.vehicle
          );
        } else {
          console.log(
            `[WARNING] No vehicle pattern matched in message: ${message}`
          );
        }
      }
    } catch (error) {
      console.error(`[ERROR] Manual extraction failed:`, error);
    }

    return extracted;
  }

  // Validate extracted data against database
  static async validateExtractedData(collected, userId) {
    console.log(
      `[INFO] Validating extracted data against database for user ${userId}`
    );

    const validationResult = {
      isValid: true,
      issues: [],
      vehicleValidation: null,
      addressValidation: null,
    };

    // Check if we have complete vehicle data to validate
    const hasCompleteVehicle =
      collected.vehicle &&
      collected.vehicle.make &&
      collected.vehicle.model &&
      collected.vehicle.year;

    // Validate vehicle data if present and complete (skip if already confirmed)
    if (hasCompleteVehicle && !collected.vehicle._confirmed) {
      try {
        const vehicleCheck = await VehicleRepository.checkVehicleExists(
          collected.vehicle,
          userId
        );
        validationResult.vehicleValidation = {
          exists: vehicleCheck.exists,
          data: collected.vehicle,
          existingVehicle: vehicleCheck.vehicle,
        };

        if (!vehicleCheck.exists) {
          validationResult.isValid = false;
          validationResult.issues.push({
            type: "vehicle_not_found",
            message: `Vehicle ${collected.vehicle.make} ${collected.vehicle.model} ${collected.vehicle.year} not found in your garage`,
            data: collected.vehicle,
          });
        }
      } catch (error) {
        console.error(`[ERROR] Vehicle validation failed:`, error);
        validationResult.issues.push({
          type: "vehicle_validation_error",
          message: "Unable to validate vehicle data",
          error: error.message,
        });
      }
    } else if (hasCompleteVehicle && collected.vehicle._confirmed) {
      console.log(`[INFO] Vehicle already confirmed, skipping validation`);
    }

    // Check if we have complete address data to validate
    const hasCompleteAddress =
      collected.location &&
      collected.location.address &&
      collected.location.city &&
      collected.location.state;

    // Validate address data if present and complete (skip if already confirmed)
    if (hasCompleteAddress && !collected.location._confirmed) {
      try {
        const addressCheck = await AddressRepository.checkAddressExists(
          collected.location,
          userId
        );
        validationResult.addressValidation = {
          exists: addressCheck.exists,
          data: collected.location,
          existingAddress: addressCheck.address,
        };

        if (!addressCheck.exists) {
          validationResult.isValid = false;
          validationResult.issues.push({
            type: "address_not_found",
            message: `Address not found in your saved locations`,
            data: collected.location,
          });
        }
      } catch (error) {
        console.error(`[ERROR] Address validation failed:`, error);
        validationResult.issues.push({
          type: "address_validation_error",
          message: "Unable to validate address data",
          error: error.message,
        });
      }
    } else if (hasCompleteAddress && collected.location._confirmed) {
      console.log(`[INFO] Address already confirmed, skipping validation`);
    }

    console.log(
      `[INFO] Validation completed. Valid: ${validationResult.isValid}, Issues: ${validationResult.issues.length}`
    );
    return validationResult;
  }

  // Handle validation issues by asking user for confirmation
  static async handleValidationIssues(validationResult, collected) {
    console.log(`[INFO] Handling validation issues:`, validationResult.issues);

    let response = "I found some information that needs confirmation:\n\n";

    for (const issue of validationResult.issues) {
      if (issue.type === "vehicle_not_found") {
        const vehicle = issue.data;
        response += `🚗 **Vehicle Not Found**: I couldn't find a ${vehicle.make} ${vehicle.model} ${vehicle.year}`;
        if (vehicle.trim) response += ` ${vehicle.trim}`;
        response += ` in your garage.\n\n`;
        response += `Would you like me to add this vehicle to your garage so we can proceed with the service request?\n\n`;
        response += `**Vehicle Details:**\n`;
        response += `- Make: ${vehicle.make}\n`;
        response += `- Model: ${vehicle.model}\n`;
        response += `- Year: ${vehicle.year}\n`;
        if (vehicle.trim) response += `- Trim: ${vehicle.trim}\n`;
        if (vehicle.engine) response += `- Engine: ${vehicle.engine}\n`;
        if (vehicle.transmission)
          response += `- Transmission: ${vehicle.transmission}\n`;
        response += `\nPlease reply with "yes" to add this vehicle, or provide different vehicle details.\n\n`;
      }

      if (issue.type === "address_not_found") {
        const location = issue.data;
        response += `📍 **Address Not Found**: I couldn't find this address in your saved locations.\n\n`;
        response += `Would you like me to add this address to your saved locations so we can proceed?\n\n`;
        response += `**Address Details:**\n`;
        if (location.address) response += `- Address: ${location.address}\n`;
        if (location.city) response += `- City: ${location.city}\n`;
        if (location.state) response += `- State: ${location.state}\n`;
        if (location.zipCode || location.zip)
          response += `- ZIP: ${location.zipCode || location.zip}\n`;
        if (location.addressType)
          response += `- Type: ${location.addressType}\n`;
        response += `\nPlease reply with "yes" to add this address, or provide a different address.\n\n`;
      }
    }

    // Removed "skip validation" option to keep the flow simple

    return response;
  }

  // Check if user wants to use the service request creation tool
  static shouldUseServiceRequestTool(message, collected) {
    console.log(
      `[INFO] Checking if should use service request tool for message: ${message}`
    );

    // Patterns that indicate user wants to create service request directly
    const createPatterns = [
      /create.*service.*request/i,
      /book.*service/i,
      /schedule.*service/i,
      /make.*appointment/i,
      /use.*tool/i,
      /direct.*create/i,
      /skip.*validation/i,
    ];

    // Check if user explicitly wants to use the tool
    const wantsToUseToolDirectly = createPatterns.some((pattern) =>
      pattern.test(message)
    );

    // Check if we have enough data and user is confirming
    const hasEnoughData = collected.vehicle && collected.location;
    const isConfirming =
      /^(yes|yeah|yep|sure|ok|okay|confirm|proceed|go ahead)$/i.test(
        message.trim()
      );

    const shouldUse = wantsToUseToolDirectly || (hasEnoughData && isConfirming);

    console.log(`[DEBUG] Should use service request tool: ${shouldUse}`);
    console.log(
      `[DEBUG] - Wants to use tool directly: ${wantsToUseToolDirectly}`
    );
    console.log(`[DEBUG] - Has enough data: ${hasEnoughData}`);
    console.log(`[DEBUG] - Is confirming: ${isConfirming}`);

    return shouldUse;
  }

  // Check if user is confirming to add missing data to database
  static async isUserConfirmingAddition(message, validationResult) {
    console.log(
      `[INFO] Checking if user is confirming addition for message: ${message}`
    );

    // Simple confirmation patterns
    const confirmationPatterns = [
      /^(yes|yeah|yep|sure|ok|okay|confirm|proceed|go ahead|add it|add them)$/i,
      /^(y|yes please|sure thing|absolutely|definitely)$/i,
    ];

    const isConfirming = confirmationPatterns.some((pattern) =>
      pattern.test(message.trim())
    );
    const hasValidationIssues =
      validationResult &&
      !validationResult.isValid &&
      validationResult.issues.length > 0;

    console.log(`[DEBUG] - Is confirming: ${isConfirming}`);
    console.log(`[DEBUG] - Has validation issues: ${hasValidationIssues}`);

    return isConfirming && hasValidationIssues;
  }

  // Generate next step prompt based on what's still missing
  static async generateNextStepPrompt(collected) {
    console.log(
      `[INFO] Generating next step prompt for collected data:`,
      collected
    );

    // Check what's missing
    const hasVehicle =
      collected.vehicle && Object.keys(collected.vehicle).length > 0;
    const hasLocation =
      collected.location && Object.keys(collected.location).length > 0;
    const hasAppointment =
      collected.appointment && Object.keys(collected.appointment).length > 1; // More than just isUrgent

    console.log(`[DEBUG] - Has vehicle: ${hasVehicle}`);
    console.log(`[DEBUG] - Has location: ${hasLocation}`);
    console.log(`[DEBUG] - Has appointment: ${hasAppointment}`);

    if (!hasVehicle) {
      return "Great! Now, could you please provide your vehicle details (make, model, year)?";
    } else if (!hasLocation) {
      return "Perfect! Now, where would you like the service to take place? Please provide the address.";
    } else if (!hasAppointment) {
      return "Excellent! When would you like to schedule the service? Please provide your preferred date and time.";
    } else {
      // All basic info collected, ask for final confirmation
      return `Great! I have all the information I need:

🚗 **Vehicle**: ${collected.vehicle.year} ${collected.vehicle.make} ${
        collected.vehicle.model
      }
📍 **Location**: ${collected.location.address || "Address provided"}
📅 **Service**: As requested

Would you like me to create the service request now?`;
    }
  }
}
