import { databaseService } from "../db/database.service.js";

export async function saveServiceRequest(collected, userId) {
  console.log(`[INFO] 🔥 saveServiceRequest called with userId: ${userId}`);
  console.log(`[DEBUG] userId type: ${typeof userId}, value: ${userId}`);
  console.log(`[INFO] Collected data:`, JSON.stringify(collected, null, 2));

  const ds = databaseService.getDataSource();
  console.log(`[INFO] Database connection obtained`);

  try {
    // Step 1: Check or create Vehicle
    console.log(`[INFO] Step 1: Processing vehicle data...`);
    const vehicleCheckQuery = `
      SELECT * FROM "Vehicle"
      WHERE "userId" = $1
        AND "industry" = $2
        AND "model" = $3
        AND "year" = $4
        AND "vehicleTrim" = $5
        AND "engine" = $6
        AND "driveTrain" = $7
        AND "status" != 'deleted'
      LIMIT 1;
    `;
    const vehicleCheckValues = [
      userId,
      collected.vehicle?.make || null,
      collected.vehicle?.model || null,
      collected.vehicle?.year || null,
      collected.vehicle?.trim || null,
      collected.vehicle?.engine || null,
      collected.vehicle?.transmission || null,
    ];

    console.log(`[INFO] Vehicle check query values:`, vehicleCheckValues);
    let vehicleResult = await ds.query(vehicleCheckQuery, vehicleCheckValues);
    console.log(`[INFO] Vehicle check result:`, vehicleResult);

    let vehicle;
    if (vehicleResult.length > 0) {
      vehicle = vehicleResult[0];
      console.log(`[INFO] Found existing vehicle:`, vehicle);
    } else {
      console.log(`[INFO] Creating new vehicle...`);
      const vehicleQuery = `
        INSERT INTO "Vehicle" (
          "userId", "industry", "model", "year", "vehicleTrim", "engine", "driveTrain", "status", "createdAt", "updatedAt"
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'active', NOW(), NOW()) RETURNING *;
      `;
      vehicleResult = await ds.query(vehicleQuery, vehicleCheckValues);
      vehicle = vehicleResult[0];
      console.log(`[SUCCESS] Created new vehicle:`, vehicle);
    }

    // Step 2: Check or create Address
    console.log(`[INFO] Step 2: Processing address data...`);
    const addressCheckQuery = `
      SELECT * FROM "Address"
      WHERE "userId" = $1
        AND "addressLine1" = $2
        AND "city" = $3
        AND "state" = $4
        AND "zipcode" = $5
        AND "addressType" = $6
      LIMIT 1;
    `;
    const addressCheckValues = [
      userId,
      collected.location?.address || null,
      collected.location?.city || null,
      collected.location?.state || null,
      collected.location?.zipCode || null,
      collected.location?.addressType || "home",
    ];

    console.log(`[INFO] Address check query values:`, addressCheckValues);
    let addressResult = await ds.query(addressCheckQuery, addressCheckValues);
    console.log(`[INFO] Address check result:`, addressResult);

    let address;
    if (addressResult.length > 0) {
      address = addressResult[0];
      console.log(`[INFO] Found existing address:`, address);
    } else {
      console.log(`[INFO] Creating new address...`);
      const addressQuery = `
        INSERT INTO "Address" (
          "userId", "addressLine1", "city", "state", "zipcode", "addressType", "createdAt", "updatedAt"
        ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW()) RETURNING *;
      `;
      addressResult = await ds.query(addressQuery, addressCheckValues);
      address = addressResult[0];
      console.log(`[SUCCESS] Created new address:`, address);
    }

    // Step 3: Find provider ID from User table
    let providerId = null;
    if (collected.provider?.name) {
      console.log(
        `[INFO] Step 3a: Looking up provider ID for: ${collected.provider.name}`
      );

      // Try multiple search strategies
      let providerResult = [];

      // Strategy 1: If we have the provider ID from the collected data, use it directly
      if (collected.provider?.id) {
        const idQuery = `
          SELECT "id", "firstName", "lastName" FROM "User"
          WHERE "id" = $1
          AND "role" = 'PROVIDER'
          LIMIT 1;
        `;
        console.log(`[DEBUG] Executing ID-based provider query:`, idQuery);
        console.log(`[DEBUG] Query parameters:`, [collected.provider.id]);
        providerResult = await ds.query(idQuery, [collected.provider.id]);
        console.log(
          `[INFO] ID-based match result:`,
          JSON.stringify(providerResult, null, 2)
        );
      }

      // Strategy 2: Exact match with CONCAT (if no ID or ID lookup failed)
      if (providerResult.length === 0) {
        const exactQuery = `
          SELECT "id", "firstName", "lastName" FROM "User"
          WHERE CONCAT("firstName", ' ', "lastName") = $1
          AND "role" = 'PROVIDER'
          LIMIT 1;
        `;
        console.log(
          `[DEBUG] Executing exact match provider query:`,
          exactQuery
        );
        console.log(`[DEBUG] Query parameters:`, [collected.provider.name]);
        providerResult = await ds.query(exactQuery, [collected.provider.name]);
        console.log(
          `[INFO] Exact match result:`,
          JSON.stringify(providerResult, null, 2)
        );
      }

      // Strategy 3: Case-insensitive search
      if (providerResult.length === 0) {
        const caseInsensitiveQuery = `
          SELECT "id", "firstName", "lastName" FROM "User"
          WHERE LOWER(CONCAT("firstName", ' ', "lastName")) = LOWER($1)
          AND "role" = 'PROVIDER'
          LIMIT 1;
        `;
        console.log(
          `[DEBUG] Executing case-insensitive provider query:`,
          caseInsensitiveQuery
        );
        console.log(`[DEBUG] Query parameters:`, [collected.provider.name]);
        providerResult = await ds.query(caseInsensitiveQuery, [
          collected.provider.name,
        ]);
        console.log(
          `[INFO] Case-insensitive match result:`,
          JSON.stringify(providerResult, null, 2)
        );
      }

      // Strategy 4: Partial name search
      if (providerResult.length === 0) {
        const partialQuery = `
          SELECT "id", "firstName", "lastName" FROM "User"
          WHERE ("firstName" ILIKE $1 OR "lastName" ILIKE $2)
          AND "role" = 'PROVIDER'
          LIMIT 1;
        `;
        const nameParts = collected.provider.name.split(" ");
        const firstName = nameParts[0] || "";
        const lastName = nameParts[1] || "";
        console.log(
          `[DEBUG] Executing partial match provider query:`,
          partialQuery
        );
        console.log(`[DEBUG] Query parameters:`, [
          `%${firstName}%`,
          `%${lastName}%`,
        ]);
        providerResult = await ds.query(partialQuery, [
          `%${firstName}%`,
          `%${lastName}%`,
        ]);
        console.log(
          `[INFO] Partial match result:`,
          JSON.stringify(providerResult, null, 2)
        );
      }

      if (providerResult.length > 0) {
        providerId = providerResult[0].id;
        console.log(
          `[SUCCESS] Found provider ID: ${providerId} for ${collected.provider.name} (${providerResult[0].firstName} ${providerResult[0].lastName})`
        );
      } else {
        console.warn(
          `[WARNING] Provider not found in User table: ${collected.provider.name}`
        );
        console.log(
          `[INFO] Available search strategies exhausted. Provider may not exist in database.`
        );
      }
    } else {
      console.log(
        `[INFO] Step 3b: No specific provider requested - will auto-assign available provider`
      );
    }

    // Step 3c: Auto-assign provider if none specified or found
    if (!providerId) {
      console.log(`[INFO] Step 3c: Auto-assigning available provider...`);
      const autoAssignQuery = `
        SELECT "id", "firstName", "lastName" FROM "User"
        WHERE "role" = 'PROVIDER'
        AND "status" = 'active'
        AND "firstName" IS NOT NULL
        AND "lastName" IS NOT NULL
        ORDER BY "firstName"
        LIMIT 1;
      `;
      console.log(
        `[DEBUG] Executing auto-assign provider query:`,
        autoAssignQuery
      );
      const autoAssignResult = await ds.query(autoAssignQuery);
      console.log(
        `[INFO] Auto-assign result:`,
        JSON.stringify(autoAssignResult, null, 2)
      );

      if (autoAssignResult.length > 0) {
        providerId = autoAssignResult[0].id;
        console.log(
          `[SUCCESS] Auto-assigned provider: ${autoAssignResult[0].firstName} ${autoAssignResult[0].lastName} (ID: ${providerId})`
        );
      } else {
        console.log(
          `[WARNING] No available providers found for auto-assignment`
        );
        // Allow service request to be created without provider
        providerId = null;
      }
    }

    // Step 4: Create ServiceRequest
    console.log(`[INFO] Step 4: Creating service request...`);
    const serviceRequestQuery = `
      INSERT INTO "ServiceRequest" (
        "userId", "vehicleId", "addressId", "providerId", "scheduledAt", "propertyLocation", "specialInstruction", "serviceDuration", "note", "status", "createdAt", "updatedAt"
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 'pending', NOW(), NOW()) RETURNING *;
    `;
    const serviceRequestValues = [
      userId,
      vehicle.id,
      address.id,
      providerId,
      collected.appointment?.scheduledAt || null,
      collected.location?.addressType === "home" ? "garage" : "other",
      collected.instructions?.text || null,
      collected.appointment?.isUrgent ? "urgent" : "normal",
      collected.instructions?.text || null, // Store instructions in note field as well
    ];

    console.log(`[DEBUG] Service request query:`, serviceRequestQuery);
    console.log(
      `[DEBUG] Service request query values:`,
      JSON.stringify(serviceRequestValues, null, 2)
    );
    console.log(`[INFO] 🚀 EXECUTING SERVICE REQUEST INSERT QUERY...`);

    const serviceRequestResult = await ds.query(
      serviceRequestQuery,
      serviceRequestValues
    );

    console.log(
      `[DEBUG] Raw service request result:`,
      JSON.stringify(serviceRequestResult, null, 2)
    );
    const serviceRequest = serviceRequestResult[0];
    console.log(
      `[SUCCESS] ✅ Created service request:`,
      JSON.stringify(serviceRequest, null, 2)
    );

    // Optionally, add provider and service option logic here as needed
    console.log(`[INFO] Service request creation completed successfully`);

    const result = { serviceRequest, vehicle, address };
    console.log(`[SUCCESS] 🎉 DATABASE SAVE COMPLETED SUCCESSFULLY!`);
    console.log(`[SUCCESS] 📊 Final result:`, JSON.stringify(result, null, 2));
    console.log(`[SUCCESS] 🔢 Service Request ID: ${serviceRequest.id}`);
    console.log(`[SUCCESS] 👤 Provider ID: ${serviceRequest.providerId}`);
    console.log(`[SUCCESS] 🚗 Vehicle ID: ${serviceRequest.vehicleId}`);
    console.log(`[SUCCESS] 📍 Address ID: ${serviceRequest.addressId}`);
    return result;
  } catch (error) {
    console.error(`[ERROR] Database operation failed:`, error);
    console.error(`[ERROR] Error details:`, {
      message: error.message,
      stack: error.stack,
      code: error.code,
    });
    throw error;
  }
}
