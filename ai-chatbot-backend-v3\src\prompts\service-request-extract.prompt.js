import { PromptTemplate } from "@langchain/core/prompts";

export const serviceRequestExtractPrompt = PromptTemplate.fromTemplate(`
Given the following conversation history and the latest user message, extract the following fields as a single JSON object:
- vehicle (make, model, year, trim, engine, transmission)
- appointment (date, time, isUrgent)
- location (address, city, state, zip, addressType)
- provider (name, id, preference)
- instructions (text, hasInstructions)

If a field is missing, set it to null.

Conversation history:
{history}

User message:
{message}

Return only a single JSON object and nothing else. Do not include any explanation, summary, or extra text. Your response must start with '{{' and end with '}}'.
`);
