import { PromptTemplate } from "@langchain/core/prompts";

export const serviceRequestExtractPrompt = PromptTemplate.fromTemplate(`
You are a data extraction assistant. Extract service request information from the user's message and conversation history.

Conversation history:
{history}

User message:
{message}

Extract the following information and return ONLY a valid JSON object with these exact field names:

{
  "vehicle": {
    "make": "extracted make or null",
    "model": "extracted model or null",
    "year": "extracted year or null",
    "trim": "extracted trim or null",
    "engine": "extracted engine or null",
    "transmission": "extracted transmission or null"
  },
  "appointment": {
    "date": "extracted date or null",
    "time": "extracted time or null",
    "isUrgent": false
  },
  "location": {
    "address": "extracted address or null",
    "city": "extracted city or null",
    "state": "extracted state or null",
    "zip": "extracted zip or null",
    "addressType": "extracted type (home/work/other) or null"
  },
  "provider": {
    "name": "extracted provider name or null",
    "id": null,
    "preference": "extracted preference or null"
  },
  "instructions": {
    "text": "extracted instructions or null",
    "hasInstructions": false
  }
}

IMPORTANT RULES:
1. Return ONLY the JSON object, no other text
2. Use null for missing values, not empty strings
3. Keep the exact structure shown above
4. For the current message "Chevrolet Pilot 2020 Limited, 3.5L V6, CVT", extract:
   - make: "Chevrolet"
   - model: "Pilot"
   - year: "2020"
   - trim: "Limited"
   - engine: "3.5L V6"
   - transmission: "CVT"
5. Set all other fields to null since they weren't provided

Your response must be valid JSON that starts with { and ends with }.
`);
