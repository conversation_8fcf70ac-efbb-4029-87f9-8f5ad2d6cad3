import { PromptTemplate } from "@langchain/core/prompts";

export const serviceRequestExtractPrompt = PromptTemplate.fromTemplate(`
Extract from: {message}
History: {history}

Return JSON:
{{"vehicle":{{"make":null,"model":null,"year":null,"trim":null,"engine":null,"transmission":null}},"appointment":{{"date":null,"time":null,"isUrgent":false}},"location":{{"address":null,"city":null,"state":null,"zip":null,"addressType":null}},"provider":{{"name":null,"id":null,"preference":null}},"instructions":{{"text":null,"hasInstructions":false}}}}

For provider preference: "auto" if user wants auto-assignment, provider name if specific provider requested.

Use null for missing. JSON only.
`);
