import React from "react";
import VoiceInput from "./VoiceInput.jsx";
import styles from "../styles/ChatApp.module.css";

const ChatInput = ({
  input,
  setInput,
  onSendMessage,
  listening,
  onStartListening,
  onStopListening,
  isLoading,
  micPermissionDenied,
}) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      onSendMessage(input);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className={styles.inputSection}>
      <form onSubmit={handleSubmit} className={styles.inputForm}>
        <div className={styles.inputContainer}>
          <button
            type="button"
            className={styles.attachButton}
            title="Attach file"
          >
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
              <path
                d="M21.44 11.05L12.25 20.24C11.1242 21.3658 9.59722 21.9983 8.005 21.9983C6.41278 21.9983 4.88583 21.3658 3.76 20.24C2.63417 19.1142 2.00166 17.5872 2.00166 15.995C2.00166 14.4028 2.63417 12.8758 3.76 11.75L12.95 2.56C13.7006 1.80944 14.7186 1.38776 15.78 1.38776C16.8414 1.38776 17.8594 1.80944 18.61 2.56C19.3606 3.31056 19.7822 4.32861 19.7822 5.39C19.7822 6.45139 19.3606 7.46944 18.61 8.22L9.41 17.41C9.03494 17.7851 8.52556 17.9961 8 17.9961C7.47444 17.9961 6.96506 17.7851 6.59 17.41C6.21494 17.0349 6.00389 16.5256 6.00389 16C6.00389 15.4744 6.21494 14.9651 6.59 14.59L15.07 6.1"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask, write or search for anything..."
            className={styles.messageInput}
            disabled={listening || isLoading}
          />

          <div className={styles.inputActions}>
            <VoiceInput
              listening={listening}
              onStartListening={onStartListening}
              onStopListening={onStopListening}
              micPermissionDenied={micPermissionDenied}
            />

            <button
              type="submit"
              className={`${styles.sendButton} ${!input.trim() || isLoading || listening ? styles.disabled : ""}`}
              disabled={!input.trim() || isLoading || listening}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path
                  d="M22 2L11 13M22 2L15 22L11 13M22 2L2 9L11 13"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ChatInput;
