import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { LLMUtils } from "../utils/llm.utils.js";
import {
  generalResponsePrompt,
  providerListPrompt,
  providerDetailPrompt,
  generalStructuredPrompt,
} from "../prompts/index.js";

export class LLMService {
  static llm = new ChatGoogleGenerativeAI({
    model: "gemini-2.0-flash",
    temperature: 0.1,
    apiKey: process.env.GOOGLE_API_KEY,
  });

  static async generateResponse(input, history = "") {
    try {
      const chain = generalResponsePrompt.pipe(this.llm);
      const result = await chain.invoke({ input, history });
      return typeof result === "string" ? result : result?.content || result;
    } catch (error) {
      throw error;
    }
  }

  static async generateStructuredResponse(message, responseType = "text") {
    try {
      console.log("[INFO] Generating structured response:", message);
      let prompt;
      switch (responseType) {
        case "provider_list":
          prompt = providerListPrompt;
          break;
        case "provider_detail":
          prompt = providerDetailPrompt;
          break;
        default:
          prompt = generalStructuredPrompt;
      }
      const chain = prompt.pipe(this.llm);
      const result = await chain.invoke({ message });
      const response = LLMUtils.handleLLMResponse(
        result,
        `Structured Response (${responseType})`,
        false
      );
      if (!response.success) {
        throw new Error(response.error);
      }
      console.log(
        "[SUCCESS] Structured response generated successfully:",
        response.content
      );
      return response.content;
    } catch (error) {
      console.error("[ERROR] Structured response generation failed:", error);
      throw error;
    }
  }
}
