import { LLMService } from "./llm.service.js";
import { domainIntentExtractionPrompt } from "../prompts/domain-intent-extraction.prompt.js";
import { JsonUtils } from "../utils/json.utils.js";
import { LLMUtils } from "../utils/llm.utils.js";
import { ServiceRequestService } from "./service-request.service.js";

export class IntentService {
  // Cache for common phrases to avoid LLM calls
  static commonPhrasesCache = new Map([
    // Greetings
    [
      "hi",
      {
        domain: "general",
        intent: "greeting",
        query: "hi",
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
        queryType: "general",
        filters: null,
      },
    ],
    [
      "hello",
      {
        domain: "general",
        intent: "greeting",
        query: "hello",
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
        queryType: "general",
        filters: null,
      },
    ],
    [
      "hey",
      {
        domain: "general",
        intent: "greeting",
        query: "hey",
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
        queryType: "general",
        filters: null,
      },
    ],
    [
      "how are you",
      {
        domain: "general",
        intent: "greeting",
        query: "how are you",
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
        queryType: "general",
        filters: null,
      },
    ],
    [
      "hi how are you",
      {
        domain: "general",
        intent: "greeting",
        query: "hi how are you",
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
        queryType: "general",
        filters: null,
      },
    ],

    // Common provider queries
    [
      "list providers",
      {
        domain: "provider",
        intent: "provider_listing",
        query: "list providers",
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
        queryType: "general",
        filters: null,
      },
    ],
    [
      "show providers",
      {
        domain: "provider",
        intent: "provider_listing",
        query: "show providers",
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
        queryType: "general",
        filters: null,
      },
    ],
    [
      "all providers",
      {
        domain: "provider",
        intent: "provider_listing",
        query: "all providers",
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
        queryType: "general",
        filters: null,
      },
    ],

    // Help queries
    [
      "help",
      {
        domain: "general",
        intent: "help",
        query: "help",
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
        queryType: "general",
        filters: null,
      },
    ],
    [
      "what can you do",
      {
        domain: "general",
        intent: "help",
        query: "what can you do",
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
        queryType: "general",
        filters: null,
      },
    ],
  ]);

  static async extractIntentAndQuery(message, context = "", userId = null) {
    try {
      console.log("[INFO] Extracting intent and query from message:", message);

      // Check if user is in a service request flow
      const isInServiceFlow = userId
        ? this.isUserInServiceRequestFlow(userId, context)
        : false;
      console.log("[INFO] User in service request flow:", isInServiceFlow);

      // If user is in service request flow, force service_request domain
      if (isInServiceFlow) {
        console.log("[INFO] Forcing service_request domain due to active flow");
        return {
          domain: "service_request",
          intent: "service_request",
          query: message,
          entities: {
            providerName: null,
            serviceType: null,
            location: null,
            date: null,
            time: null,
            price: null,
            other: null,
          },
          queryType: "general",
          filters: null,
        };
      }

      // Skip cache and pattern matching if we have context (likely in a flow)
      const hasContext = context && context.trim().length > 0;

      if (!hasContext) {
        // Check cache first for common phrases (only when no context)
        const normalizedMessage = message.toLowerCase().trim();
        const cachedResult = this.commonPhrasesCache.get(normalizedMessage);

        if (cachedResult) {
          console.log(
            "[INFO] Using cached intent for common phrase:",
            normalizedMessage
          );
          console.log("[INFO] Intent Cache Hit - 0 LLM tokens used");
          return cachedResult;
        }

        // Pattern matching for number-based queries (e.g., "5 providers", "10 providers")
        const numberProviderPattern = /^(\d+)\s*providers?$/i;
        const match = normalizedMessage.match(numberProviderPattern);
        if (match) {
          const limit = parseInt(match[1]);
          console.log(
            "[INFO] Using pattern matching for number-based query:",
            limit
          );
          console.log("[INFO] Pattern Match Hit - 0 LLM tokens used");
          return {
            domain: "provider",
            intent: "provider_listing",
            query: `${limit} providers`,
            entities: {
              providerName: null,
              serviceType: null,
              location: null,
              date: null,
              time: null,
              price: null,
              other: null,
            },
            queryType: "general",
            filters: { limit },
          };
        }
      }

      console.log("[INFO] Cache miss - using LLM for intent extraction");
      console.log("[INFO] Context provided:", hasContext ? "Yes" : "No");

      // Combine context and message for the prompt
      const prompt = context
        ? `Context: ${context}\nUser: ${message}`
        : message;

      console.log("[INFO] Full prompt for intent extraction:", prompt);

      // Use the new domain/intent extraction prompt
      const chain = domainIntentExtractionPrompt.pipe(LLMService.llm);
      const result = await chain.invoke({ message: prompt });
      console.log("result", result);

      // Handle the LLM response properly
      const response = LLMUtils.handleLLMResponse(
        result,
        "Intent Extraction",
        true
      );

      if (!response.success) {
        throw new Error(response.error);
      }

      // Use the improved JSON parsing from JsonUtils
      const parsed = JsonUtils.parseJsonResponse(
        response.content,
        "Intent Extraction"
      );

      if (!parsed) {
        console.warn("[WARNING] Using fallback intent detection");
        return this.detectSimpleIntent(message);
      }

      // Handle compressed field names from ultra-compact prompt
      const expandedParsed = {
        domain: parsed.d || parsed.domain,
        intent: parsed.i || parsed.intent,
        query: parsed.q || parsed.query,
        entities: parsed.e || parsed.entities,
        queryType: parsed.qt || parsed.queryType,
        filters: parsed.f || parsed.filters,
      };

      console.log("[SUCCESS] Intent extracted successfully:", {
        domain: expandedParsed.domain,
        intent: expandedParsed.intent,
        query: expandedParsed.query,
        entities: expandedParsed.entities,
        queryType: expandedParsed.queryType,
        filters: expandedParsed.filters,
      });

      // Ensure all expected entity fields are present (handle compressed entity names)
      const entities = {
        providerName:
          expandedParsed.entities?.p ??
          expandedParsed.entities?.providerName ??
          null,
        serviceType:
          expandedParsed.entities?.s ??
          expandedParsed.entities?.serviceType ??
          null,
        location:
          expandedParsed.entities?.l ??
          expandedParsed.entities?.location ??
          null,
        date:
          expandedParsed.entities?.d ?? expandedParsed.entities?.date ?? null,
        time:
          expandedParsed.entities?.t ?? expandedParsed.entities?.time ?? null,
        price:
          expandedParsed.entities?.pr ?? expandedParsed.entities?.price ?? null,
        other:
          expandedParsed.entities?.o ?? expandedParsed.entities?.other ?? null,
      };

      return {
        domain: expandedParsed.domain || "provider",
        intent: expandedParsed.intent || "general_conversation",
        query: expandedParsed.query || message,
        entities,
        queryType: expandedParsed.queryType || "unknown",
        filters: expandedParsed.filters ?? null,
      };
    } catch (error) {
      console.error("[ERROR] Intent extraction failed:", error);
      return this.detectSimpleIntent(message);
    }
  }

  static detectSimpleIntent(message) {
    const lowerMessage = message.toLowerCase();

    // Simple keyword-based intent and domain detection as fallback
    if (
      lowerMessage.includes("provider") ||
      lowerMessage.includes("providers")
    ) {
      if (
        lowerMessage.includes("list") ||
        lowerMessage.includes("show") ||
        lowerMessage.includes("all")
      ) {
        return {
          domain: "provider",
          intent: "provider_listing",
          query: message,
          entities: {
            providerName: null,
            serviceType: null,
            location: null,
            date: null,
            time: null,
            price: null,
            other: null,
          },
        };
      }
      return {
        domain: "provider",
        intent: "specific_provider",
        query: message,
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
      };
    }

    if (lowerMessage.includes("service") || lowerMessage.includes("services")) {
      if (
        lowerMessage.includes("list") ||
        lowerMessage.includes("show") ||
        lowerMessage.includes("all")
      ) {
        return {
          domain: "service",
          intent: "service_listing",
          query: message,
          entities: {
            providerName: null,
            serviceType: null,
            location: null,
            date: null,
            time: null,
            price: null,
            other: null,
          },
        };
      }
      return {
        domain: "service",
        intent: "specific_service",
        query: message,
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
      };
    }

    if (
      lowerMessage.includes("database") ||
      lowerMessage.includes("query") ||
      lowerMessage.includes("data")
    ) {
      return {
        domain: "provider",
        intent: "database_query",
        query: message,
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
      };
    }

    if (
      lowerMessage.includes("hello") ||
      lowerMessage.includes("hi") ||
      lowerMessage.includes("hey")
    ) {
      return {
        domain: "general",
        intent: "greeting",
        query: message,
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
      };
    }

    if (
      lowerMessage.includes("help") ||
      lowerMessage.includes("what can you do")
    ) {
      return {
        domain: "general",
        intent: "help",
        query: message,
        entities: {
          providerName: null,
          serviceType: null,
          location: null,
          date: null,
          time: null,
          price: null,
          other: null,
        },
      };
    }

    return {
      domain: "general",
      intent: "general_conversation",
      query: message,
      entities: {
        providerName: null,
        serviceType: null,
        location: null,
        date: null,
        time: null,
        price: null,
        other: null,
      },
    };
  }

  /**
   * Check if a user is currently in a service request flow
   * @param {string|number} userId - The user ID
   * @param {string} context - The conversation context (last bot message)
   * @returns {boolean} True if user is in service request flow
   */
  static isUserInServiceRequestFlow(userId, context = "") {
    try {
      // Check if user has active service request data
      const collected = ServiceRequestService.getCollected(userId);
      const hasCollectedData = collected && Object.keys(collected).length > 0;

      // Check context for service request keywords
      const serviceRequestKeywords = [
        "service request",
        "car details",
        "appointment",
        "date and time",
        "service address",
        "provider",
        "special instructions",
        "confirm",
        "book",
        "schedule",
        "technician",
        "vehicle",
        "make",
        "model",
        "year",
      ];

      const contextLower = context.toLowerCase();
      const hasServiceContext = serviceRequestKeywords.some((keyword) =>
        contextLower.includes(keyword)
      );

      console.log(
        "[INFO] Service flow check - hasCollectedData:",
        hasCollectedData,
        "hasServiceContext:",
        hasServiceContext
      );

      return hasCollectedData || hasServiceContext;
    } catch (error) {
      console.warn(
        "[WARNING] Error checking service request flow:",
        error.message
      );
      return false;
    }
  }
}
