// User-Facing Messages
export const USER_MESSAGES = {
  // Greetings
  WELCOME: "Hi! How can I help you today?",
  GREETING:
    "Hello! I'm <PERSON><PERSON>, your AI automotive assistant. How can I help you?",

  // Provider Related
  PROVIDER_NOT_FOUND:
    'I couldn\'t find a provider named "{name}". Please check the spelling or ask for a list of available providers.',
  SPECIFY_PROVIDER_NAME:
    "I couldn't identify which provider you're asking about. Could you please specify the provider's name?",
  NO_PROVIDERS_AVAILABLE: "No providers are currently available.",

  // Help Messages
  HELP_GENERAL:
    "I can help you with:\n- Finding service providers\n- Getting provider details\n- Answering questions about vehicle services\n- General automotive assistance",
  HELP_PROVIDERS:
    "To find providers, you can:\n- Ask for 'all providers'\n- Ask about a specific provider by name\n- Ask for providers with specific services",

  // Error Messages for Users
  GENERAL_ERROR:
    "I'm sorry, I encountered an error processing your message. Please try again.",
  DATABASE_ERROR:
    "I'm sorry, I couldn't process your database query. Please try rephrasing your question.",
  PROVIDER_ERROR: "I'm sorry, I couldn't find information about that provider.",
  CONVERSATION_ERROR:
    "I'm sorry, I'm having trouble processing your message right now. Please try again later.",
  SERVER_ERROR:
    "Sorry, there was a problem connecting to the server. Please try again.",

  // Speech Recognition
  MICROPHONE_BLOCKED:
    "🎤 Microphone access is currently blocked.\n\nTo enable voice input:\n1. Click the microphone icon in your browser's address bar\n2. Select 'Allow' for microphone access\n3. Refresh the page\n\nVoice features make chatting easier - try it out!",
  SPEECH_NOT_SUPPORTED:
    "Speech recognition is not supported in this browser. Please try Chrome, Edge, or Safari.",
  SPEECH_HTTPS_REQUIRED:
    "Speech recognition requires a secure connection (HTTPS). Please use HTTPS or localhost.",
  SPEECH_PERMISSION_DENIED:
    "Microphone access is blocked.\n\nTo fix this:\n1. Look for a microphone icon in your browser's address bar\n2. Click it and select 'Always allow'\n3. Or go to browser Settings > Privacy > Microphone\n4. Refresh the page and try again",
  SPEECH_NO_SPEECH:
    "No speech was detected. Please speak clearly and try again.",
  SPEECH_NO_MICROPHONE:
    "No microphone was found.\n\nPlease check:\n1. Your microphone is connected\n2. It's not being used by another application\n3. Browser has permission to access it",
  SPEECH_NETWORK_ERROR:
    "Network error occurred during speech recognition. Please check your internet connection.",
  SPEECH_SERVICE_ERROR:
    "Speech recognition service is not available. Please try again later.",

  // Suggestions
  SUGGEST_PROVIDER_LIST:
    "Would you like me to show you all available providers?",
  SUGGEST_SPECIFIC_PROVIDER:
    "Would you like me to help you find a specific provider?",
  SUGGEST_REPHRASE: "Could you please rephrase your question?",
  SUGGEST_TRY_AGAIN: "Please try again with a different approach.",

  // Voice Input
  VOICE_BUTTON_TITLE_LISTENING: "Stop voice input",
  VOICE_BUTTON_TITLE_START: "Start voice input",
  VOICE_BUTTON_TITLE_DENIED: "Microphone access denied - click for help",

  // Loading States
  LOADING: "Processing your request...",
  SENDING: "Sending message...",

  // Placeholder Text
  INPUT_PLACEHOLDER: "Type your message here...",
  INPUT_PLACEHOLDER_VOICE: "Click the microphone to speak...",

  // Empty States
  NO_MESSAGES: "No messages yet. Start a conversation!",
  NO_PROVIDERS_FOUND: "No providers found matching your criteria.",

  // Success Messages
  MESSAGE_SENT: "Message sent successfully",
  VOICE_RECOGNIZED: "Voice input recognized",

  // Accessibility
  ACCESSIBILITY_SEND_BUTTON: "Send message",
  ACCESSIBILITY_VOICE_BUTTON: "Voice input button",
  ACCESSIBILITY_MESSAGE: "Message from {sender}",
  ACCESSIBILITY_PROVIDER_CARD: "Provider information for {name}",
  ACCESSIBILITY_SERVICE_ITEM: "Service: {service} - {price}",
};

// Error Messages
export const ERROR_MESSAGES = {
  // Network Errors
  NETWORK_ERROR:
    "Network error occurred. Please check your internet connection.",
  TIMEOUT_ERROR: "Request timed out. Please try again.",
  SERVER_UNAVAILABLE:
    "Server is currently unavailable. Please try again later.",

  // API Errors
  API_ERROR: "API request failed. Please try again.",
  INVALID_RESPONSE: "Invalid response from server. Please try again.",

  // Speech Recognition Errors
  SPEECH_RECOGNITION_NOT_SUPPORTED:
    "Speech recognition is not supported in this browser. Please try Chrome, Edge, or Safari.",
  SPEECH_RECOGNITION_HTTPS_REQUIRED:
    "Speech recognition requires a secure connection (HTTPS). Please use HTTPS or localhost.",
  SPEECH_RECOGNITION_PERMISSION_DENIED:
    "Microphone access is blocked. Please enable microphone access and try again.",
  SPEECH_RECOGNITION_NO_SPEECH:
    "No speech was detected. Please speak clearly and try again.",
  SPEECH_RECOGNITION_NO_MICROPHONE:
    "No microphone was found. Please check your microphone connection.",
  SPEECH_RECOGNITION_NETWORK_ERROR:
    "Network error occurred during speech recognition. Please check your internet connection.",
  SPEECH_RECOGNITION_SERVICE_ERROR:
    "Speech recognition service is not available. Please try again later.",

  // Validation Errors
  EMPTY_MESSAGE: "Please enter a message before sending.",
  MESSAGE_TOO_LONG: "Message is too long. Please shorten it.",
  INVALID_INPUT: "Invalid input provided. Please check your message.",

  // General Errors
  UNKNOWN_ERROR: "An unexpected error occurred. Please try again.",
  FEATURE_NOT_AVAILABLE: "This feature is not available at the moment.",
  BROWSER_NOT_SUPPORTED:
    "Your browser is not supported. Please use a modern browser.",
};

// Info Messages
export const INFO_MESSAGES = {
  // Connection Status
  CONNECTING: "Connecting to server...",
  CONNECTED: "Connected to server",
  DISCONNECTED: "Disconnected from server",

  // Speech Recognition
  SPEECH_RECOGNITION_STARTING: "Starting speech recognition...",
  SPEECH_RECOGNITION_LISTENING: "Listening...",
  SPEECH_RECOGNITION_PROCESSING: "Processing speech...",
  SPEECH_RECOGNITION_STOPPED: "Speech recognition stopped",

  // Message Status
  MESSAGE_SENDING: "Sending message...",
  MESSAGE_SENT: "Message sent",
  MESSAGE_RECEIVED: "Message received",

  // Provider Information
  PROVIDER_LOADING: "Loading provider information...",
  PROVIDER_LOADED: "Provider information loaded",
  PROVIDER_NOT_FOUND: "Provider not found",

  // General
  LOADING: "Loading...",
  PROCESSING: "Processing...",
  SAVING: "Saving...",
  UPDATING: "Updating...",
};

// Warning Messages
export const WARNING_MESSAGES = {
  // Connection
  CONNECTION_UNSTABLE: "Connection is unstable. Messages may not be delivered.",
  CONNECTION_SLOW: "Connection is slow. Please wait...",

  // Speech Recognition
  SPEECH_RECOGNITION_UNCLEAR: "Speech was unclear. Please try again.",
  SPEECH_RECOGNITION_BACKGROUND_NOISE:
    "Background noise detected. Please speak clearly.",

  // Performance
  PERFORMANCE_SLOW: "Performance is slow. Please wait...",
  MEMORY_LOW: "Low memory detected. Some features may be limited.",

  // General
  FEATURE_DEPRECATED: "This feature is deprecated and may be removed soon.",
  BROWSER_OLD:
    "You are using an older browser. Some features may not work properly.",
};

// Helper function to format messages with placeholders
export function formatMessage(message, placeholders = {}) {
  let formattedMessage = message;
  Object.keys(placeholders).forEach((key) => {
    formattedMessage = formattedMessage.replace(`{${key}}`, placeholders[key]);
  });
  return formattedMessage;
}

// Helper function to get error message by error type
export function getErrorMessage(error) {
  if (error.name === "NetworkError") {
    return ERROR_MESSAGES.NETWORK_ERROR;
  }
  if (error.name === "TimeoutError") {
    return ERROR_MESSAGES.TIMEOUT_ERROR;
  }
  if (error.message?.includes("speech recognition")) {
    return ERROR_MESSAGES.SPEECH_RECOGNITION_NOT_SUPPORTED;
  }
  if (error.message?.includes("microphone")) {
    return ERROR_MESSAGES.SPEECH_RECOGNITION_PERMISSION_DENIED;
  }
  return ERROR_MESSAGES.UNKNOWN_ERROR;
}
