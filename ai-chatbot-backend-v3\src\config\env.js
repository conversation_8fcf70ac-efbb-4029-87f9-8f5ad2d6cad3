import "dotenv/config";

export const config = {
  // Server configuration
  PORT: process.env.PORT || 3002,
  NODE_ENV: process.env.NODE_ENV || "development",

  // Database configuration
  DATABASE_URL: process.env.DATABASE_URL,

  // LLM configuration
  GOOGLE_API_KEY: process.env.GOOGLE_API_KEY,
  LLM_MODEL: process.env.LLM_MODEL || "gemini-2.0-flash",
  LLM_TEMPERATURE: parseFloat(process.env.LLM_TEMPERATURE) || 0.1,

  // Logging configuration
  LOG_LEVEL: process.env.LOG_LEVEL || "info",

  // API configuration
  API_VERSION: process.env.API_VERSION || "v1",
  CORS_ORIGIN: process.env.CORS_ORIGIN || "*",
};

// Validate required environment variables
export function validateEnvironment() {
  const required = ["DATABASE_URL", "GOOGLE_API_KEY"];
  const missing = required.filter((key) => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(", ")}`
    );
  }

  console.log("[SUCCESS] Configuration loaded successfully");
}

// Export individual config values for convenience
export const {
  PORT,
  NODE_ENV,
  DATABASE_URL,
  GOOGLE_API_KEY,
  LLM_MODEL,
  LLM_TEMPERATURE,
  LOG_LEVEL,
  API_VERSION,
  CORS_ORIGIN,
} = config;
