/**
 * Provider utility functions for filtering, validation, and name extraction
 */

/**
 * Extracts provider name from user message using pattern matching
 * @param {string} message - User message
 * @returns {string|null} - Extracted provider name or null
 */
export function extractProviderName(message) {
  try {
    // Common patterns for asking about specific providers
    const patterns = [
      /(?:tell me about|information about|details about|what about)\s+([A-Za-z\s]+)/i,
      /(?:what services does|services offered by)\s+([A-Za-z\s]+)/i,
      /(?:who is|who's)\s+([A-Za-z\s]+)/i,
      /(?:I want to know about|I want information about)\s+([A-Za-z\s]+)/i,
    ];

    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    // If no pattern matches, try to extract a name-like sequence
    const words = message.split(/\s+/);
    const nameWords = words.filter(
      (word) =>
        /^[A-Z][a-z]+$/.test(word) || /^[A-Z][a-z]+\s+[A-Z][a-z]+$/.test(word)
    );

    if (nameWords.length > 0) {
      return nameWords[0];
    }

    return null;
  } catch (error) {
    console.error("[ERROR] Error extracting provider name:", error);
    return null;
  }
}

/**
 * Validates if a provider has complete and valid information
 * @param {Object} provider - Provider object from database
 * @returns {boolean} - True if provider is valid
 */
export function isValidProvider(provider) {
  const hasValidName =
    provider.firstName &&
    provider.lastName &&
    provider.firstName.trim() !== "" &&
    provider.lastName.trim() !== "";

  const hasValidEmail = provider.email && provider.email.trim() !== "";

  return hasValidName && hasValidEmail;
}

/**
 * Filters an array of providers to only include valid ones
 * @param {Array} providers - Array of provider objects from database
 * @returns {Array} - Filtered array of valid providers
 */
export function filterValidProviders(providers) {
  console.log("[INFO] Filtering providers:", providers.length);

  const validProviders = providers.filter((provider) => {
    const isValid = isValidProvider(provider);

    console.log(
      `Provider ${provider.id}: firstName="${provider.firstName}", lastName="${provider.lastName}", email="${provider.email}", valid=${isValid}`
    );

    return isValid;
  });

  console.log("[SUCCESS] Valid providers found:", validProviders.length);
  return validProviders;
}

/**
 * Formats provider data for consistent output
 * @param {Object} provider - Raw provider object from database
 * @returns {Object} - Formatted provider object
 */
export function formatProvider(provider) {
  return {
    id: provider.id,
    name: `${provider.firstName.trim()} ${provider.lastName.trim()}`,
    email: provider.email.trim(),
    phone: provider.mobileNumber,
  };
}

/**
 * Logs provider data for debugging
 * @param {Array} providers - Array of provider objects
 * @param {string} context - Context for the log message
 */
export function logProviderData(providers, context = "Provider data") {
  console.log(`[INFO] ${context}:`, providers);
  console.log(`[INFO] Total providers: ${providers.length}`);
}
