/* Provider Card Styles */
.providerCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.providerCard:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.providerHeader {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.providerName {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.providerPhone {
  font-size: 14px;
  color: #4b5563;
  background: #dbeafe;
  padding: 4px 8px;
  border-radius: 6px;
  white-space: nowrap;
}

.providerDetails {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contactInfo {
  display: flex;
  align-items: center;
  color: #374151;
  font-size: 14px;
  gap: 8px;
}

.contactInfo .icon {
  font-size: 16px;
}

.servicesSection {
  margin-top: 12px;
}

.servicesTitle {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.servicesList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.serviceItem {
  margin-left: 16px;
  padding: 8px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.serviceName {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.serviceDescription {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 4px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.servicePrice {
  font-size: 13px;
  color: #059669;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Specific Provider Detail Styles */
.specificProvider {
  background: white;
  border-radius: 16px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.specificProviderName {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16px;
}

.contactDetails {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.contactDetail {
  display: flex;
  align-items: center;
  color: #374151;
  gap: 12px;
}

.contactDetail .icon {
  font-size: 18px;
}

.contactLabel {
  font-weight: 500;
  min-width: 60px;
}

.contactValue {
  color: #1f2937;
}

.servicesOffered {
  margin-top: 20px;
}

.servicesOfferedTitle {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.detailedServicesList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detailedServiceItem {
  background: #f9fafb;
  border-radius: 12px;
  padding: 16px;
  border-left: 4px solid #667eea;
}

.detailedServiceName {
  font-size: 18px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8px;
}

.detailedServiceDescription {
  color: #6b7280;
  margin-bottom: 8px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.detailedServicePrice {
  color: #059669;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* No Services Warning */
.noServicesWarning {
  background: #fef3cd;
  border: 1px solid #f59e0b;
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
}

.noServicesHeader {
  display: flex;
  align-items: center;
  color: #92400e;
  font-weight: 500;
  margin-bottom: 8px;
  gap: 8px;
}

.noServicesText {
  color: #b45309;
  font-size: 14px;
}

/* Provider List Actions */
.providerListActions {
  margin-top: 16px;
  padding: 12px;
  background: #dbeafe;
  border-radius: 12px;
  color: #1e40af;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Message Content */
.messageContent {
  line-height: 1.6;
}

.messageLine {
  color: #374151;
  margin-bottom: 8px;
}

.messageBold {
  font-weight: 600;
  font-size: 18px;
  color: #1f2937;
  margin: 8px 0;
}

.messageEmpty {
  margin-bottom: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .providerHeader {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .contactDetails {
    gap: 8px;
  }

  .contactDetail {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .specificProvider {
    padding: 16px;
  }

  .specificProviderName {
    font-size: 20px;
  }

  .servicesOfferedTitle {
    font-size: 18px;
  }

  .detailedServiceName {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .providerCard {
    padding: 12px;
    margin-bottom: 12px;
  }

  .specificProvider {
    padding: 12px;
  }

  .detailedServiceItem {
    padding: 12px;
  }
}
