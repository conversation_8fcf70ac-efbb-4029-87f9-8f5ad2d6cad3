import React from "react";
import styles from "../styles/MessageFormatter.module.css";

// ===== PROVIDER CARD COMPONENT =====
const ProviderCard = ({ provider, index, isDetailed = false }) => {
  return (
    <div className={styles.providerCard}>
      <div className={styles.providerHeader}>
        <h3 className={styles.providerName}>
          {index && `${index}. `}
          {provider.name}
        </h3>
        {provider.phone && (
          <span className={styles.providerPhone}>📞 {provider.phone}</span>
        )}
      </div>

      <div className={styles.providerDetails}>
        {provider.email && (
          <div className={styles.contactInfo}>
            <span className={styles.icon}>📧</span>
            <span>{provider.email}</span>
          </div>
        )}

        {isDetailed && provider.services && provider.services.length > 0 && (
          <div className={styles.servicesSection}>
            <h4 className={styles.servicesTitle}>🚗 Services:</h4>
            <div className={styles.servicesList}>
              {provider.services.map((service, serviceIndex) => (
                <div key={serviceIndex} className={styles.serviceItem}>
                  <div className={styles.serviceName}>• {service.name}</div>
                  {service.description && (
                    <div className={styles.serviceDescription}>
                      📝 {service.description}
                    </div>
                  )}
                  {service.price && (
                    <div className={styles.servicePrice}>
                      💰 {service.price}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// ===== SPECIFIC PROVIDER DETAIL COMPONENT =====
const SpecificProviderDetail = ({ provider }) => {
  return (
    <div className={styles.specificProvider}>
      <div>
        <h2 className={styles.specificProviderName}>{provider.name}</h2>

        <div className={styles.contactDetails}>
          {provider.email && (
            <div className={styles.contactDetail}>
              <span className={styles.icon}>📧</span>
              <span className={styles.contactLabel}>Email:</span>
              <span className={styles.contactValue}>{provider.email}</span>
            </div>
          )}

          {provider.phone && (
            <div className={styles.contactDetail}>
              <span className={styles.icon}>📞</span>
              <span className={styles.contactLabel}>Phone:</span>
              <span className={styles.contactValue}>{provider.phone}</span>
            </div>
          )}
        </div>
      </div>

      {provider.services && provider.services.length > 0 ? (
        <div className={styles.servicesOffered}>
          <h3 className={styles.servicesOfferedTitle}>🚗 Services Offered:</h3>
          <div className={styles.detailedServicesList}>
            {provider.services.map((service, index) => (
              <div key={index} className={styles.detailedServiceItem}>
                <h4 className={styles.detailedServiceName}>
                  {index + 1}. {service.name}
                </h4>
                {service.description && (
                  <div className={styles.detailedServiceDescription}>
                    <span>📝</span>
                    <span>{service.description}</span>
                  </div>
                )}
                {service.price && (
                  <div className={styles.detailedServicePrice}>
                    <span>💰</span>
                    <span>Price: {service.price}</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className={styles.noServicesWarning}>
          <div className={styles.noServicesHeader}>
            <span>⚠️</span>
            <span>No services listed yet</span>
          </div>
          <p className={styles.noServicesText}>
            This provider hasn't added their services to our system yet. You can
            contact them directly for more information.
          </p>
        </div>
      )}
    </div>
  );
};

// ===== MAIN FORMATTER COMPONENT =====
const MessageFormatter = ({ message }) => {
  if (!message) return null;

  // If message is a string, fallback to plain rendering
  if (typeof message === "string") {
    return <div className={styles.messageContent}>{message}</div>;
  }

  // Handle the new simplified format: { message: string, data: any }
  if (message.message !== undefined) {
    return (
      <div>
        {/* Display the message text */}
        <div className={styles.messageContent}>
          <div className={styles.messageText}>{message.message}</div>
        </div>

        {/* Display data if present */}
        {message.data && (
          <>
            {/* Add spacing between message and data */}
            <div style={{ marginBottom: "16px" }}></div>

            {/* Display data based on its structure */}
            {Array.isArray(message.data) && message.data.length > 0 ? (
              // Provider list
              message.data.map((provider, index) => (
                <ProviderCard
                  key={provider.id || index}
                  provider={provider}
                  index={index + 1}
                  isDetailed={true}
                />
              ))
            ) : message.data && !Array.isArray(message.data) ? (
              // Single provider detail
              <SpecificProviderDetail provider={message.data} />
            ) : null}
          </>
        )}
      </div>
    );
  }

  // Handle legacy format for backward compatibility
  if (message.type === "provider_detail" && message.provider) {
    return (
      <div>
        {message.message && (
          <div className={styles.messageContent}>
            <div className={styles.messageText}>{message.message}</div>
          </div>
        )}
        {message.message && <div style={{ marginBottom: "16px" }}></div>}
        <SpecificProviderDetail provider={message.provider} />
      </div>
    );
  }

  if (message.type === "provider_list" && Array.isArray(message.providers)) {
    return (
      <div>
        {message.message && (
          <div className={styles.messageContent}>
            <div className={styles.messageText}>{message.message}</div>
          </div>
        )}
        {message.message && <div style={{ marginBottom: "16px" }}></div>}
        {message.providers.map((provider, index) => (
          <ProviderCard
            key={provider.id || index}
            provider={provider}
            index={index + 1}
            isDetailed={true}
          />
        ))}
      </div>
    );
  }

  if (message.type === "text" && message.text) {
    return <div className={styles.messageContent}>{message.text}</div>;
  }

  // fallback for unknown structure
  return <div className={styles.messageContent}>{JSON.stringify(message)}</div>;
};

export default MessageFormatter;
