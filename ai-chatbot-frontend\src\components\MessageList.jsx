import React from "react";
import MessageItem from "./MessageItem.jsx";
import styles from "../styles/ChatApp.module.css";

const MessageList = ({ messages, messagesEndRef }) => {
  return (
    <div className={styles.messagesArea}>
      <div className={styles.messagesContent}>
        {messages.length === 0 ? (
          <div className={styles.welcomeState}>
            <div className={styles.welcomeAvatar}>
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" fill="url(#welcomeGradient)" />
                <path
                  d="M9.5 9.5C9.5 8.11929 10.6193 7 12 7C13.3807 7 14.5 8.11929 14.5 9.5"
                  stroke="white"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                />
                <circle cx="12" cy="15" r="1" fill="white" />
                <defs>
                  <linearGradient
                    id="welcomeGradient"
                    x1="0%"
                    y1="0%"
                    x2="100%"
                    y2="100%"
                  >
                    <stop offset="0%" stopColor="#8B5CF6" />
                    <stop offset="100%" stopColor="#A855F7" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <h2 className={styles.welcomeTitle}>Hello! I'm Revi</h2>
            <p className={styles.welcomeDescription}>
              Your AI automotive assistant with voice capabilities. You can type
              or speak to me!
            </p>
            <div className={styles.featureCards}>
              <div className={styles.featureCard}>
                <span className={styles.featureIcon}>🎤</span>
                <span className={styles.featureText}>Voice Input</span>
              </div>
              <div className={styles.featureCard}>
                <span className={styles.featureIcon}>🔊</span>
                <span className={styles.featureText}>Voice Output</span>
              </div>
              <div className={styles.featureCard}>
                <span className={styles.featureIcon}>🚗</span>
                <span className={styles.featureText}>Car Expert</span>
              </div>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => (
              <MessageItem
                key={message.id || index}
                message={message}
                isLast={index === messages.length - 1}
              />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>
    </div>
  );
};

export default MessageList;
