# Chatbot V3 - Modular Architecture

## 🏗️ Architecture Overview

This version implements a **modular architecture** with **shared repositories** for better scalability and maintainability.

### 📁 Current Structure

```
src/
├── modules/                    # Domain-specific modules
│   └── provider/              # Provider domain (implemented)
│       ├── services/          # Business logic
│       ├── detectors/         # LLM-based detection
│       ├── fetchers/          # Data fetching
│       └── formatters/        # Response formatting
│
├── shared/                    # Shared components
│   ├── repositories/          # Shared data access layer
│   ├── database/              # Database configuration
│   ├── detectors/             # Base detection classes
│   ├── fetchers/              # Base fetching classes
│   ├── formatters/            # Base formatting classes
│   └── utils/                 # Common utilities
│
├── core/                      # Core application
│   ├── services/              # Main business logic
│   ├── controllers/           # Request handlers
│   └── routes/                # API routes
│
└── config/                    # Configuration files
```

## 🎯 Key Features

### ✅ **Shared Repositories**
- Single source of truth for data access
- Consistent patterns across modules
- Shared database connections and caching

### ✅ **Module Isolation**
- Provider domain is self-contained
- Independent development and testing
- Clear boundaries between features

### ✅ **Base Classes**
- Common patterns and utilities
- Consistent error handling
- Shared logging and validation

### ✅ **Scalable Architecture**
- Easy to add new modules when needed
- Easy to add new features
- Enterprise-level structure

## 🚀 Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment:**
   ```bash
   cp env.example .env
   # Edit .env with your database and API keys
   ```

3. **Start the server:**
   ```bash
   npm start
   ```

4. **Test the API:**
   ```bash
   curl -X POST http://localhost:3002/api/chat \
     -H "Content-Type: application/json" \
     -d '{"message": "Show me providers with more than 2 services"}'
   ```

## 📊 API Endpoints

- `POST /api/chat` - Main chatbot endpoint
- `GET /api/health` - Health check

## 🔧 Adding New Modules (When Needed)

1. **Create module structure:**
   ```bash
   mkdir -p src/modules/new-module/{services,detectors,fetchers,formatters}
   ```

2. **Create shared repository:**
   ```bash
   # src/shared/repositories/new-module.repository.js
   ```

3. **Implement module components:**
   - Service logic
   - Detection patterns
   - Data fetching
   - Response formatting

4. **Register in core service:**
   ```javascript
   // src/core/services/chatbot.service.js
   import { NewModuleService } from "../../modules/new-module/index.js";
   ```

## 🎯 Benefits

- **Maintainability**: Clear separation of concerns
- **Scalability**: Easy to add new features when needed
- **Performance**: Shared resources and caching
- **Consistency**: Common patterns across modules
- **Testing**: Independent module testing
- **Clean Code**: Only necessary files and folders

## 🔄 Migration from V2

The new structure maintains backward compatibility while providing:
- Better organization
- Improved scalability
- Enhanced maintainability
- Future-proof architecture

## 📋 Current Implementation

### ✅ **Provider Module**
- Provider listing with service count filtering
- Specific provider details
- Service-based provider search
- LLM-based intent detection

### 🔄 **Ready for Future Modules**
- Service management
- Service request handling
- Analytics and reporting
- Any other domain-specific features

---

**Built with ❤️ using Node.js, Express, TypeORM, and LangChain**
