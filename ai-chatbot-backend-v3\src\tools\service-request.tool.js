import { VehicleRepository } from "../repositories/vehicle.repository.js";
import { AddressRepository } from "../repositories/address.repository.js";
import { saveServiceRequest } from "../repositories/service-request.repository.js";
import { ProviderRepository } from "../repositories/provider.repository.js";

export const serviceRequestTool = {
  /**
   * Main tool function that can be called from chat flow
   * @param {Object} params - Tool parameters
   * @returns {Object} - Tool response
   */
  async call({ message, userId, sessionId, action = "create" }) {
    console.log(
      `[INFO] ServiceRequestTool called - action: ${action}, userId: ${userId}`
    );

    try {
      switch (action) {
        case "create":
          return await this.createServiceRequest({
            message,
            userId,
            sessionId,
          });
        case "validate":
          return await this.validateData({ message, userId, sessionId });
        case "list_vehicles":
          return await this.listUserVehicles({ userId });
        case "list_addresses":
          return await this.listUserAddresses({ userId });
        default:
          return {
            success: false,
            message: `Unknown action: ${action}`,
          };
      }
    } catch (error) {
      console.error(`[ERROR] ServiceRequestTool failed:`, error);
      return {
        success: false,
        message: `Service request tool error: ${error.message}`,
      };
    }
  },

  /**
   * Create a service request with validation and auto-creation of missing data
   * @param {Object} params - Parameters
   * @returns {Object} - Result of service request creation
   */
  async createServiceRequest({ message, userId, sessionId }) {
    console.log(
      `[INFO] ServiceRequestTool: Creating service request for user ${userId}`
    );

    try {
      // Extract data from message (you can integrate with your existing extraction logic)
      const extractedData = await this.extractDataFromMessage(message, userId);

      const result = {
        success: false,
        serviceRequest: null,
        vehicle: null,
        address: null,
        provider: null,
        validationIssues: [],
        createdEntities: [],
      };

      // Step 1: Validate and handle vehicle
      if (extractedData.vehicle) {
        const vehicleResult = await this.handleVehicle(
          extractedData.vehicle,
          userId
        );
        result.vehicle = vehicleResult.vehicle;
        if (vehicleResult.created) {
          result.createdEntities.push({
            type: "vehicle",
            data: vehicleResult.vehicle,
          });
        }
        if (vehicleResult.issues) {
          result.validationIssues.push(...vehicleResult.issues);
        }
      }

      // Step 2: Validate and handle address
      if (extractedData.location) {
        const addressResult = await this.handleAddress(
          extractedData.location,
          userId
        );
        result.address = addressResult.address;
        if (addressResult.created) {
          result.createdEntities.push({
            type: "address",
            data: addressResult.address,
          });
        }
        if (addressResult.issues) {
          result.validationIssues.push(...addressResult.issues);
        }
      }

      // Step 3: Handle provider
      if (extractedData.provider && extractedData.provider.id) {
        const provider = await ProviderRepository.findProviderById(
          extractedData.provider.id
        );
        result.provider = provider;
      } else {
        // Auto-assign a provider
        const providers = await ProviderRepository.findAvailableProviders(1);
        if (providers.length > 0) {
          result.provider = providers[0];
          console.log(`[INFO] Auto-assigned provider:`, result.provider);
        }
      }

      // Step 4: Create service request if all required data is available
      if (result.vehicle && result.address) {
        const serviceRequestData = {
          vehicle: this.formatVehicleForSave(result.vehicle),
          location: this.formatAddressForSave(result.address),
          appointment: extractedData.appointment || {},
          provider: result.provider ? { id: result.provider.id } : {},
          instructions: extractedData.instructions || {},
        };

        const savedResult = await saveServiceRequest(
          serviceRequestData,
          userId
        );
        result.serviceRequest = savedResult.serviceRequest;
        result.success = true;

        return {
          success: true,
          message: `✅ Service request created successfully! Request ID: ${result.serviceRequest.id}`,
          data: result,
        };
      } else {
        result.success = false;
        if (!result.vehicle) {
          result.validationIssues.push({
            type: "missing_vehicle",
            message: "Vehicle information is required",
          });
        }
        if (!result.address) {
          result.validationIssues.push({
            type: "missing_address",
            message: "Address information is required",
          });
        }

        return {
          success: false,
          message: "Missing required information for service request",
          data: result,
        };
      }
    } catch (error) {
      console.error(
        `[ERROR] ServiceRequestTool: Failed to create service request:`,
        error
      );
      return {
        success: false,
        message: `Failed to create service request: ${error.message}`,
      };
    }
  },

  /**
   * Extract data from message using the existing service request extraction logic
   */
  async extractDataFromMessage(message, userId) {
    // Import the service request service to use its extraction logic
    const { ServiceRequestService } = await import(
      "../services/service-request.service.js"
    );

    try {
      // Get current collected data
      const currentCollected = ServiceRequestService.getCollected(userId);

      // Use the existing extraction logic
      const extractedData = await ServiceRequestService.extractAndUpdateData(
        message,
        "", // empty history for now
        currentCollected,
        userId
      );

      console.log(
        `[DEBUG] Extracted data from message:`,
        JSON.stringify(extractedData, null, 2)
      );
      return extractedData;
    } catch (error) {
      console.error(`[ERROR] Failed to extract data from message:`, error);
      // Return a basic structure if extraction fails
      return {
        vehicle: null,
        location: null,
        appointment: null,
        provider: null,
        instructions: null,
      };
    }
  },

  /**
   * Handle vehicle validation and creation
   * @param {Object} vehicleData - Vehicle data
   * @param {number} userId - User ID
   * @returns {Object} - Vehicle handling result
   */
  async handleVehicle(vehicleData, userId) {
    console.log(`[INFO] Handling vehicle for user ${userId}:`, vehicleData);

    try {
      // Check if vehicle exists
      const existsResult = await VehicleRepository.checkVehicleExists(
        vehicleData,
        userId
      );

      if (existsResult.exists) {
        console.log(
          `[INFO] Vehicle found in database:`,
          existsResult.vehicle.id
        );
        return {
          vehicle: existsResult.vehicle,
          created: false,
          issues: [],
        };
      } else {
        // Vehicle doesn't exist, create it
        console.log(`[INFO] Vehicle not found, creating new vehicle`);
        const newVehicle = await VehicleRepository.createVehicle(
          vehicleData,
          userId
        );
        return {
          vehicle: newVehicle,
          created: true,
          issues: [],
        };
      }
    } catch (error) {
      console.error(`[ERROR] Vehicle handling failed:`, error);
      return {
        vehicle: null,
        created: false,
        issues: [{ type: "vehicle_error", message: error.message }],
      };
    }
  },

  /**
   * Handle address validation and creation
   * @param {Object} addressData - Address data
   * @param {number} userId - User ID
   * @returns {Object} - Address handling result
   */
  async handleAddress(addressData, userId) {
    console.log(`[INFO] Handling address for user ${userId}:`, addressData);

    try {
      // Validate address format first
      const validation = AddressRepository.validateAddress(addressData);
      if (!validation.isValid) {
        return {
          address: null,
          created: false,
          issues: validation.errors.map((error) => ({
            type: "address_validation",
            message: error,
          })),
        };
      }

      // Check if address exists
      const existsResult = await AddressRepository.checkAddressExists(
        addressData,
        userId
      );

      if (existsResult.exists) {
        console.log(
          `[INFO] Address found in database:`,
          existsResult.address.id
        );
        return {
          address: existsResult.address,
          created: false,
          issues: [],
        };
      } else {
        // Address doesn't exist, create it
        console.log(`[INFO] Address not found, creating new address`);
        const newAddress = await AddressRepository.createAddress(
          addressData,
          userId
        );
        return {
          address: newAddress,
          created: true,
          issues: [],
        };
      }
    } catch (error) {
      console.error(`[ERROR] Address handling failed:`, error);
      return {
        address: null,
        created: false,
        issues: [{ type: "address_error", message: error.message }],
      };
    }
  },

  /**
   * Format vehicle data for saving
   * @param {Object} vehicle - Vehicle object from database
   * @returns {Object} - Formatted vehicle data
   */
  formatVehicleForSave(vehicle) {
    return {
      make: vehicle.industry,
      model: vehicle.model,
      year: vehicle.year,
      trim: vehicle.vehicleTrim,
      engine: vehicle.engine,
      transmission: vehicle.driveTrain,
    };
  },

  /**
   * Format address data for saving
   * @param {Object} address - Address object from database
   * @returns {Object} - Formatted address data
   */
  formatAddressForSave(address) {
    return {
      address: address.addressLine1,
      city: address.city,
      state: address.state,
      zipCode: address.zipcode,
      addressType: address.addressType,
    };
  },

  /**
   * Validate data from message
   */
  async validateData({ message, userId, sessionId }) {
    console.log(
      `[INFO] ServiceRequestTool: Validating data for user ${userId}`
    );

    try {
      const extractedData = await this.extractDataFromMessage(message, userId);
      const validationResult = {
        isValid: true,
        issues: [],
      };

      // Add validation logic here
      return {
        success: true,
        message: "Data validation completed",
        data: validationResult,
      };
    } catch (error) {
      return {
        success: false,
        message: `Validation failed: ${error.message}`,
      };
    }
  },

  /**
   * List user's vehicles
   */
  async listUserVehicles({ userId }) {
    try {
      const vehicles = await VehicleRepository.getUserVehicles(userId);
      return {
        success: true,
        message: `Found ${vehicles.length} vehicles`,
        data: vehicles,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get vehicles: ${error.message}`,
      };
    }
  },

  /**
   * List user's addresses
   */
  async listUserAddresses({ userId }) {
    try {
      const addresses = await AddressRepository.getUserAddresses(userId);
      return {
        success: true,
        message: `Found ${addresses.length} addresses`,
        data: addresses,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get addresses: ${error.message}`,
      };
    }
  },
};
