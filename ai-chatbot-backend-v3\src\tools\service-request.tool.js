import { VehicleRepository } from "../repositories/vehicle.repository.js";
import { AddressRepository } from "../repositories/address.repository.js";
import { saveServiceRequest } from "../repositories/service-request.repository.js";
import { ProviderRepository } from "../repositories/provider.repository.js";

export class ServiceRequestTool {
  /**
   * Create a service request with validation and auto-creation of missing data
   * @param {Object} requestData - Service request data
   * @param {number} userId - User ID
   * @returns {Object} - Result of service request creation
   */
  static async createServiceRequest(requestData, userId) {
    console.log(`[INFO] ServiceRequestTool: Creating service request for user ${userId}`);
    console.log(`[DEBUG] Request data:`, JSON.stringify(requestData, null, 2));

    try {
      const result = {
        success: false,
        serviceRequest: null,
        vehicle: null,
        address: null,
        provider: null,
        validationIssues: [],
        createdEntities: []
      };

      // Step 1: Validate and handle vehicle
      if (requestData.vehicle) {
        const vehicleResult = await this.handleVehicle(requestData.vehicle, userId);
        result.vehicle = vehicleResult.vehicle;
        if (vehicleResult.created) {
          result.createdEntities.push({ type: 'vehicle', data: vehicleResult.vehicle });
        }
        if (vehicleResult.issues) {
          result.validationIssues.push(...vehicleResult.issues);
        }
      }

      // Step 2: Validate and handle address
      if (requestData.location) {
        const addressResult = await this.handleAddress(requestData.location, userId);
        result.address = addressResult.address;
        if (addressResult.created) {
          result.createdEntities.push({ type: 'address', data: addressResult.address });
        }
        if (addressResult.issues) {
          result.validationIssues.push(...addressResult.issues);
        }
      }

      // Step 3: Handle provider
      if (requestData.provider && requestData.provider.id) {
        const provider = await ProviderRepository.findProviderById(requestData.provider.id);
        result.provider = provider;
      } else {
        // Auto-assign a provider
        const providers = await ProviderRepository.findAvailableProviders(1);
        if (providers.length > 0) {
          result.provider = providers[0];
          console.log(`[INFO] Auto-assigned provider:`, result.provider);
        }
      }

      // Step 4: Create service request if all required data is available
      if (result.vehicle && result.address) {
        const serviceRequestData = {
          vehicle: this.formatVehicleForSave(result.vehicle),
          location: this.formatAddressForSave(result.address),
          appointment: requestData.appointment || {},
          provider: result.provider ? { id: result.provider.id } : {},
          instructions: requestData.instructions || {}
        };

        const savedResult = await saveServiceRequest(serviceRequestData, userId);
        result.serviceRequest = savedResult.serviceRequest;
        result.success = true;

        console.log(`[SUCCESS] Service request created successfully:`, result.serviceRequest.id);
      } else {
        result.success = false;
        if (!result.vehicle) {
          result.validationIssues.push({ type: 'missing_vehicle', message: 'Vehicle information is required' });
        }
        if (!result.address) {
          result.validationIssues.push({ type: 'missing_address', message: 'Address information is required' });
        }
      }

      return result;
    } catch (error) {
      console.error(`[ERROR] ServiceRequestTool: Failed to create service request:`, error);
      throw new Error(`Failed to create service request: ${error.message}`);
    }
  }

  /**
   * Handle vehicle validation and creation
   * @param {Object} vehicleData - Vehicle data
   * @param {number} userId - User ID
   * @returns {Object} - Vehicle handling result
   */
  static async handleVehicle(vehicleData, userId) {
    console.log(`[INFO] Handling vehicle for user ${userId}:`, vehicleData);

    try {
      // Check if vehicle exists
      const existsResult = await VehicleRepository.checkVehicleExists(vehicleData, userId);
      
      if (existsResult.exists) {
        console.log(`[INFO] Vehicle found in database:`, existsResult.vehicle.id);
        return {
          vehicle: existsResult.vehicle,
          created: false,
          issues: []
        };
      } else {
        // Vehicle doesn't exist, create it
        console.log(`[INFO] Vehicle not found, creating new vehicle`);
        const newVehicle = await VehicleRepository.createVehicle(vehicleData, userId);
        return {
          vehicle: newVehicle,
          created: true,
          issues: []
        };
      }
    } catch (error) {
      console.error(`[ERROR] Vehicle handling failed:`, error);
      return {
        vehicle: null,
        created: false,
        issues: [{ type: 'vehicle_error', message: error.message }]
      };
    }
  }

  /**
   * Handle address validation and creation
   * @param {Object} addressData - Address data
   * @param {number} userId - User ID
   * @returns {Object} - Address handling result
   */
  static async handleAddress(addressData, userId) {
    console.log(`[INFO] Handling address for user ${userId}:`, addressData);

    try {
      // Validate address format first
      const validation = AddressRepository.validateAddress(addressData);
      if (!validation.isValid) {
        return {
          address: null,
          created: false,
          issues: validation.errors.map(error => ({ type: 'address_validation', message: error }))
        };
      }

      // Check if address exists
      const existsResult = await AddressRepository.checkAddressExists(addressData, userId);
      
      if (existsResult.exists) {
        console.log(`[INFO] Address found in database:`, existsResult.address.id);
        return {
          address: existsResult.address,
          created: false,
          issues: []
        };
      } else {
        // Address doesn't exist, create it
        console.log(`[INFO] Address not found, creating new address`);
        const newAddress = await AddressRepository.createAddress(addressData, userId);
        return {
          address: newAddress,
          created: true,
          issues: []
        };
      }
    } catch (error) {
      console.error(`[ERROR] Address handling failed:`, error);
      return {
        address: null,
        created: false,
        issues: [{ type: 'address_error', message: error.message }]
      };
    }
  }

  /**
   * Format vehicle data for saving
   * @param {Object} vehicle - Vehicle object from database
   * @returns {Object} - Formatted vehicle data
   */
  static formatVehicleForSave(vehicle) {
    return {
      make: vehicle.industry,
      model: vehicle.model,
      year: vehicle.year,
      trim: vehicle.vehicleTrim,
      engine: vehicle.engine,
      transmission: vehicle.driveTrain
    };
  }

  /**
   * Format address data for saving
   * @param {Object} address - Address object from database
   * @returns {Object} - Formatted address data
   */
  static formatAddressForSave(address) {
    return {
      address: address.addressLine1,
      city: address.city,
      state: address.state,
      zipCode: address.zipcode,
      addressType: address.addressType
    };
  }

  /**
   * Get user's vehicles
   * @param {number} userId - User ID
   * @returns {Array} - User's vehicles
   */
  static async getUserVehicles(userId) {
    return await VehicleRepository.getUserVehicles(userId);
  }

  /**
   * Get user's addresses
   * @param {number} userId - User ID
   * @returns {Array} - User's addresses
   */
  static async getUserAddresses(userId) {
    return await AddressRepository.getUserAddresses(userId);
  }

  /**
   * Find similar vehicles for suggestions
   * @param {Object} vehicleData - Vehicle data to match
   * @param {number} userId - User ID
   * @returns {Array} - Similar vehicles
   */
  static async findSimilarVehicles(vehicleData, userId) {
    return await VehicleRepository.findSimilarVehicles(vehicleData, userId);
  }

  /**
   * Find similar addresses for suggestions
   * @param {Object} addressData - Address data to match
   * @param {number} userId - User ID
   * @returns {Array} - Similar addresses
   */
  static async findSimilarAddresses(addressData, userId) {
    return await AddressRepository.findSimilarAddresses(addressData, userId);
  }
}
