import { VehicleRepository } from "../repositories/vehicle.repository.js";
import { AddressRepository } from "../repositories/address.repository.js";
import { saveServiceRequest } from "../repositories/service-request.repository.js";
import { ProviderRepository } from "../repositories/provider.repository.js";

export const serviceRequestTool = {
  /**
   * Main tool function that can be called from chat flow
   * @param {Object} params - Tool parameters
   * @returns {Object} - Tool response
   */
  async call({ message, userId, sessionId, action = "create" }) {
    console.log(
      `[INFO] ServiceRequestTool called - action: ${action}, userId: ${userId}`
    );

    try {
      switch (action) {
        case "create":
          return await this.createServiceRequest({
            message,
            userId,
            sessionId,
          });
        case "validate":
          return await this.validateData({ message, userId, sessionId });
        case "list_vehicles":
          return await this.listUserVehicles({ userId });
        case "list_addresses":
          return await this.listUserAddresses({ userId });
        default:
          return {
            success: false,
            message: `Unknown action: ${action}`,
          };
      }
    } catch (error) {
      console.error(`[ERROR] ServiceRequestTool failed:`, error);
      return {
        success: false,
        message: `Service request tool error: ${error.message}`,
      };
    }
  },

  /**
   * Create a service request with validation and auto-creation of missing data
   * @param {Object} params - Parameters
   * @returns {Object} - Result of service request creation
   */
  async createServiceRequest({ message, userId, sessionId }) {
    console.log(
      `[INFO] ServiceRequestTool: Creating service request for user ${userId}`
    );

    try {
      // Extract data from message (you can integrate with your existing extraction logic)
      const extractedData = await this.extractDataFromMessage(message, userId);

      const result = {
        success: false,
        serviceRequest: null,
        vehicle: null,
        address: null,
        provider: null,
        validationIssues: [],
        createdEntities: [],
      };

      // Step 1: Validate and handle vehicle
      if (extractedData.vehicle) {
        const vehicleResult = await this.handleVehicle(
          extractedData.vehicle,
          userId
        );
        result.vehicle = vehicleResult.vehicle;
        if (vehicleResult.created) {
          result.createdEntities.push({
            type: "vehicle",
            data: vehicleResult.vehicle,
          });
        }
        if (vehicleResult.issues) {
          result.validationIssues.push(...vehicleResult.issues);
        }
      }

      // Step 2: Validate and handle address
      if (extractedData.location) {
        const addressResult = await this.handleAddress(
          extractedData.location,
          userId
        );
        result.address = addressResult.address;
        if (addressResult.created) {
          result.createdEntities.push({
            type: "address",
            data: addressResult.address,
          });
        }
        if (addressResult.issues) {
          result.validationIssues.push(...addressResult.issues);
        }
      }

      // Step 3: Handle provider - only if user specified one
      if (extractedData.provider && extractedData.provider.id) {
        const provider = await ProviderRepository.findProviderById(
          extractedData.provider.id
        );
        result.provider = provider;
        console.log(`[INFO] User specified provider:`, result.provider);
      } else {
        // Don't auto-assign - let user choose or ask for auto-assignment
        console.log(`[INFO] No provider specified by user`);
        result.provider = null;
      }

      // Step 4: Check what information is missing and provide helpful response
      const missingInfo = [];
      if (!result.vehicle) {
        missingInfo.push("vehicle details (make, model, year)");
      }
      if (!result.address) {
        missingInfo.push("service location/address");
      }
      if (!result.provider) {
        missingInfo.push("service provider selection");
      }

      if (missingInfo.length > 0) {
        result.success = false;

        let response =
          "I'd be happy to help you create a service request! I need a few more details:\n\n";

        if (!result.vehicle) {
          response +=
            "🚗 **Vehicle Information**: Please provide your vehicle details (make, model, year, etc.)\n";
          result.validationIssues.push({
            type: "missing_vehicle",
            message: "Vehicle information is required",
          });
        }

        if (!result.address) {
          response +=
            "📍 **Service Location**: Where would you like the service to take place?\n";
          result.validationIssues.push({
            type: "missing_address",
            message: "Address information is required",
          });
        }

        if (!result.provider) {
          response +=
            "👨‍🔧 **Service Provider**: Would you like me to auto-assign a provider or do you have a preference?\n";
          result.validationIssues.push({
            type: "missing_provider",
            message: "Provider selection needed",
          });
        }

        response +=
          "\nPlease provide the missing information and I'll create your service request!";

        return {
          success: false,
          message: response,
          data: result,
        };
      } else {
        // All required data is available - create the service request
        const serviceRequestData = {
          vehicle: this.formatVehicleForSave(result.vehicle),
          location: this.formatAddressForSave(result.address),
          appointment: extractedData.appointment || {},
          provider: result.provider ? { id: result.provider.id } : {},
          instructions: extractedData.instructions || {},
        };

        const savedResult = await saveServiceRequest(
          serviceRequestData,
          userId
        );
        result.serviceRequest = savedResult.serviceRequest;
        result.success = true;

        return {
          success: true,
          message: `✅ Service request created successfully! Request ID: ${result.serviceRequest.id}`,
          data: result,
        };
      }
    } catch (error) {
      console.error(
        `[ERROR] ServiceRequestTool: Failed to create service request:`,
        error
      );
      return {
        success: false,
        message: `Failed to create service request: ${error.message}`,
      };
    }
  },

  /**
   * Extract data from message using the existing service request extraction logic
   */
  async extractDataFromMessage(message, userId) {
    // Import the service request service to use its extraction logic
    const { ServiceRequestService } = await import(
      "../services/service-request.service.js"
    );

    try {
      // Get current collected data
      const currentCollected = ServiceRequestService.getCollected(userId);

      // Use the existing extraction logic
      const extractedData = await ServiceRequestService.extractAndUpdateData(
        message,
        "", // empty history for now
        currentCollected,
        userId
      );

      console.log(
        `[DEBUG] Extracted data from message:`,
        JSON.stringify(extractedData, null, 2)
      );
      return extractedData;
    } catch (error) {
      console.error(`[ERROR] Failed to extract data from message:`, error);
      // Return a basic structure if extraction fails
      return {
        vehicle: null,
        location: null,
        appointment: null,
        provider: null,
        instructions: null,
      };
    }
  },

  /**
   * Handle vehicle validation and creation
   * @param {Object} vehicleData - Vehicle data
   * @param {number} userId - User ID
   * @returns {Object} - Vehicle handling result
   */
  async handleVehicle(vehicleData, userId) {
    console.log(`[INFO] Handling vehicle for user ${userId}:`, vehicleData);

    try {
      // Check if vehicle exists
      const existsResult = await VehicleRepository.checkVehicleExists(
        vehicleData,
        userId
      );

      if (existsResult.exists) {
        console.log(
          `[INFO] Vehicle found in database:`,
          existsResult.vehicle.id
        );
        return {
          vehicle: existsResult.vehicle,
          created: false,
          issues: [],
        };
      } else {
        // Vehicle doesn't exist, create it
        console.log(`[INFO] Vehicle not found, creating new vehicle`);
        const newVehicle = await VehicleRepository.createVehicle(
          vehicleData,
          userId
        );
        return {
          vehicle: newVehicle,
          created: true,
          issues: [],
        };
      }
    } catch (error) {
      console.error(`[ERROR] Vehicle handling failed:`, error);
      return {
        vehicle: null,
        created: false,
        issues: [{ type: "vehicle_error", message: error.message }],
      };
    }
  },

  /**
   * Handle address validation and creation
   * @param {Object} addressData - Address data
   * @param {number} userId - User ID
   * @returns {Object} - Address handling result
   */
  async handleAddress(addressData, userId) {
    console.log(`[INFO] Handling address for user ${userId}:`, addressData);

    try {
      // Validate address format first
      const validation = AddressRepository.validateAddress(addressData);
      if (!validation.isValid) {
        return {
          address: null,
          created: false,
          issues: validation.errors.map((error) => ({
            type: "address_validation",
            message: error,
          })),
        };
      }

      // Check if address exists
      const existsResult = await AddressRepository.checkAddressExists(
        addressData,
        userId
      );

      if (existsResult.exists) {
        console.log(
          `[INFO] Address found in database:`,
          existsResult.address.id
        );
        return {
          address: existsResult.address,
          created: false,
          issues: [],
        };
      } else {
        // Address doesn't exist, create it
        console.log(`[INFO] Address not found, creating new address`);
        const newAddress = await AddressRepository.createAddress(
          addressData,
          userId
        );
        return {
          address: newAddress,
          created: true,
          issues: [],
        };
      }
    } catch (error) {
      console.error(`[ERROR] Address handling failed:`, error);
      return {
        address: null,
        created: false,
        issues: [{ type: "address_error", message: error.message }],
      };
    }
  },

  /**
   * Format vehicle data for saving
   * @param {Object} vehicle - Vehicle object from database
   * @returns {Object} - Formatted vehicle data
   */
  formatVehicleForSave(vehicle) {
    return {
      make: vehicle.industry,
      model: vehicle.model,
      year: vehicle.year,
      trim: vehicle.vehicleTrim,
      engine: vehicle.engine,
      transmission: vehicle.driveTrain,
    };
  },

  /**
   * Format address data for saving
   * @param {Object} address - Address object from database
   * @returns {Object} - Formatted address data
   */
  formatAddressForSave(address) {
    return {
      address: address.addressLine1,
      city: address.city,
      state: address.state,
      zipCode: address.zipcode,
      addressType: address.addressType,
    };
  },

  /**
   * Validate data from message
   */
  async validateData({ message, userId, sessionId }) {
    console.log(
      `[INFO] ServiceRequestTool: Validating data for user ${userId}`
    );

    try {
      const extractedData = await this.extractDataFromMessage(message, userId);
      const validationResult = {
        isValid: true,
        issues: [],
      };

      // Add validation logic here
      return {
        success: true,
        message: "Data validation completed",
        data: validationResult,
      };
    } catch (error) {
      return {
        success: false,
        message: `Validation failed: ${error.message}`,
      };
    }
  },

  /**
   * List user's vehicles
   */
  async listUserVehicles({ userId }) {
    try {
      const vehicles = await VehicleRepository.getUserVehicles(userId);
      return {
        success: true,
        message: `Found ${vehicles.length} vehicles`,
        data: vehicles,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get vehicles: ${error.message}`,
      };
    }
  },

  /**
   * List user's addresses
   */
  async listUserAddresses({ userId }) {
    try {
      const addresses = await AddressRepository.getUserAddresses(userId);
      return {
        success: true,
        message: `Found ${addresses.length} addresses`,
        data: addresses,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to get addresses: ${error.message}`,
      };
    }
  },
};
