import { PORT, validateEnvironment } from "./src/config/env.js";
import { databaseService } from "./src/db/connection.js";
import app from "./app.js";

// Global error handlers
process.on("uncaughtException", (error) => {
  console.error("[ERROR] Uncaught Exception:", error);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("[ERROR] Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

// Validate environment variables
validateEnvironment();

// Initialize database
try {
  await databaseService.initialize();
  console.log("[SUCCESS] Database connected successfully");
} catch (error) {
  console.error("[ERROR] Database connection failed:", error.message);
  process.exit(1);
}

// Start server
const server = app.listen(PORT, () => {
  console.log(`[SUCCESS] Chatbot V3 server running on port ${PORT}`);
  console.log(`[INFO] Main endpoint: POST http://localhost:${PORT}/api/chat`);
  console.log(`[INFO] Health check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("[INFO] SIGTERM received, shutting down gracefully...");
  server.close(() => {
    console.log("[SUCCESS] Server closed");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  console.log("[INFO] SIGINT received, shutting down gracefully...");
  console.log("[SUCCESS] Server closed");
  process.exit(0);
});
