import { runAgent } from "../tools/agent.js";

export const chat = async (req, res) => {
  try {
    const { message, userId = 4, sessionId = 101 } = req.body;

    console.log(`[INFO] Chat controller - Received message: "${message}"`);
    console.log(`[DEBUG] Raw request body:`, JSON.stringify(req.body, null, 2));
    console.log(
      `[INFO] Chat controller - userId: ${userId}, sessionId: ${sessionId}`
    );
    console.log(`[DEBUG] userId type: ${typeof userId}, value: ${userId}`);

    // Use LangChain agent with multiple tools - Gemini Flash 2.0 will choose the right tool
    const agentInput = `User ID: ${userId}, Session ID: ${sessionId}, Message: ${message}`;
    const agentResult = await runAgent(agentInput);

    // Extract the response from agent result
    const response = {
      message:
        agentResult.output ||
        agentResult.result ||
        "I'm sorry, I couldn't process your request.",
    };

    console.log(
      `[INFO] Chat controller - Response generated:`,
      JSON.stringify(response, null, 2)
    );

    res.json({
      success: true,
      response,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("[ERROR] Controller error:", error);
    console.error("[ERROR] Error details:", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      error: "Internal server error",
      message: error.message,
    });
  }
};

export const health = (req, res) => {
  res.json({
    success: true,
    message: "Chatbot V3 is running!",
    timestamp: new Date().toISOString(),
    version: "3.0.0",
  });
};
