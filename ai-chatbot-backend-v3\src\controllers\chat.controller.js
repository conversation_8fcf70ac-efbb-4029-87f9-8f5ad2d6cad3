import { intentTool } from "../tools/intent.tool.js";

export const chat = async (req, res) => {
  try {
    const { message, userId = 4, sessionId = 101 } = req.body;

    console.log(`[INFO] Chat controller - Received message: "${message}"`);
    console.log(`[DEBUG] Raw request body:`, JSON.stringify(req.body, null, 2));
    console.log(
      `[INFO] Chat controller - userId: ${userId}, sessionId: ${sessionId}`
    );
    console.log(`[DEBUG] userId type: ${typeof userId}, value: ${userId}`);

    // Call intentTool - it will handle context retrieval internally
    const response = await intentTool.call({
      message,
      userId,
      sessionId,
    });

    console.log(
      `[INFO] Chat controller - Response generated:`,
      JSON.stringify(response, null, 2)
    );

    res.json({
      success: true,
      response,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("[ERROR] Controller error:", error);
    console.error("[ERROR] Error details:", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      error: "Internal server error",
      message: error.message,
    });
  }
};

export const health = (req, res) => {
  res.json({
    success: true,
    message: "Chatbot V3 is running!",
    timestamp: new Date().toISOString(),
    version: "3.0.0",
  });
};
