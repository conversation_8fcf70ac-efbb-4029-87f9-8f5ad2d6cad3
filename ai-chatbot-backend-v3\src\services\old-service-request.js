import { serviceRequestPrompts } from "../prompts/service-request.prompt.js";
import { LLMService } from "./llm.service.js";
import { serviceRequestLLMFlowPrompt } from "../prompts/service-request-llm-flow.prompt.js";
import * as chatMemory from "./chat-memory.service.js";
import { saveServiceRequest } from "../repositories/service-request.repository.js";

// const userStates = {}; // In-memory; replace with DB/session for production
// const stepInstructions = [
//   "Ask the user for their car details (make, model, year) in a friendly, conversational way.",
//   "Ask the user for their preferred date and time for the service in a friendly, conversational way.",
//   "Ask the user for their full service address and type in a friendly, conversational way.",
//   "Ask the user if they have a preferred technician or if any available is fine, in a friendly, conversational way.",
//   "Ask the user if they have any special instructions for the service in a friendly, conversational way."
// ];
const llmUserStates = {}; // In-memory for LLM-driven flow

export class ServiceRequestService {
  // static steps = ["car", "appointment", "address", "provider", "instructions"];
  // static prompts = [
  //   serviceRequestPrompts.askCar,
  //   serviceRequestPrompts.askAppointment,
  //   serviceRequestPrompts.askAddress,
  //   serviceRequestPrompts.askProvider,
  //   serviceRequestPrompts.askInstructions,
  // ];

  // static async handle(userId, message) {
  //   if (!userStates[userId]) {
  //     userStates[userId] = { step: 0, data: {} };
  //   }
  //   const state = userStates[userId];
  //   // Save previous answer
  //   if (state.step > 0) {
  //     const prevKey = ServiceRequestService.steps[state.step - 1];
  //     state.data[prevKey] = message;
  //   }
  //   // If all steps done, confirm and clear state
  //   if (state.step >= ServiceRequestService.steps.length) {
  //     // TODO: Create the actual service request here
  //     const summary = JSON.stringify(state.data, null, 2);
  //     delete userStates[userId];
  //     return { message: `${serviceRequestPrompts.confirm}\n${summary}` };
  //   }
  //   // Ask next question using LLM for all steps
  //   const instruction = stepInstructions[state.step];
  //   const prompt = await LLMService.generateResponse(instruction);
  //   state.step += 1;
  //   return { message: prompt };
  // }

  // LLM-driven, prompt-only flow with chat memory and DB save
  static async handleLLMFlow({ userId, message, sessionId }) {
    console.log(
      `[INFO] ServiceRequest handleLLMFlow - userId: ${userId}, sessionId: ${sessionId}`
    );
    console.log(`[INFO] User message: ${message}`);

    // Always retrieve collected data internally
    const collected = ServiceRequestService.getCollected(userId);
    console.log(
      `[INFO] Current collected data:`,
      JSON.stringify(collected, null, 2)
    );

    // Append user message to chat memory
    if (sessionId) {
      await chatMemory.appendToHistory(sessionId, "USER", message);
    }

    // Get formatted history from chat memory
    const history = sessionId
      ? await chatMemory.getFormattedHistory(sessionId)
      : "";

    // Extract and update collected data from user message and history
    const updatedCollected = await ServiceRequestService.extractAndUpdateData(
      message,
      history,
      collected,
      userId
    );
    console.log(
      `[INFO] Updated collected data:`,
      JSON.stringify(updatedCollected, null, 2)
    );

    const prompt = serviceRequestLLMFlowPrompt
      .replace("{collected}", JSON.stringify(updatedCollected, null, 2))
      .replace("{history}", history);

    const response = await LLMService.generateResponse(prompt);
    console.log(`[INFO] LLM response: ${response}`);

    // Append assistant response to chat memory
    if (sessionId) {
      await chatMemory.appendToHistory(sessionId, "ASSISTANT", response);
    }

    // Check if all required details are collected and user confirmed
    const allDetailsCollected =
      ServiceRequestService.isAllDetailsCollected(updatedCollected);
    const isConfirmation =
      ServiceRequestService.isConfirmationMessage(response) ||
      ServiceRequestService.isUserConfirmation(message);

    console.log(`[INFO] All details collected: ${allDetailsCollected}`);
    console.log(`[INFO] Is confirmation message: ${isConfirmation}`);
    console.log(`[INFO] Required fields check:`, {
      vehicle: !!updatedCollected.vehicle,
      appointment: !!updatedCollected.appointment,
      location: !!updatedCollected.location,
      provider: !!updatedCollected.provider,
      instructions: !!updatedCollected.instructions,
    });

    console.log(`[DEBUG] Save conditions check:`);
    console.log(`[DEBUG] - allDetailsCollected: ${allDetailsCollected}`);
    console.log(`[DEBUG] - isConfirmation: ${isConfirmation}`);
    console.log(
      `[DEBUG] - Should save: ${allDetailsCollected && isConfirmation}`
    );

    if (allDetailsCollected && isConfirmation) {
      console.log(`\n🔥🔥🔥 SAVING TO DATABASE 🔥🔥🔥`);
      console.log(
        `[INFO] ✅ ATTEMPTING TO SAVE SERVICE REQUEST TO DATABASE...`
      );
      console.log(`[DEBUG] User ID: ${userId} (type: ${typeof userId})`);
      console.log(`[DEBUG] Session ID: ${sessionId}`);
      console.log(`[DEBUG] All details collected: ${allDetailsCollected}`);
      console.log(`[DEBUG] Is confirmation: ${isConfirmation}`);

      // Create a clean JSON structure for database saving
      const dataForDatabase = {
        userId: userId,
        vehicle: updatedCollected.vehicle || {},
        appointment: updatedCollected.appointment || {},
        location: updatedCollected.location || {},
        provider: updatedCollected.provider || {},
        instructions: updatedCollected.instructions || {},
        serviceType: updatedCollected.serviceType || "brake repair",
        timestamp: new Date().toISOString(),
      };

      console.log(`[DEBUG] 📋 CLEAN DATA STRUCTURE FOR DATABASE:`);
      console.log(JSON.stringify(dataForDatabase, null, 2));

      try {
        console.log(`[INFO] 🚀 CALLING saveServiceRequest function...`);
        const saveResult = await saveServiceRequest(updatedCollected, userId);
        console.log(`[SUCCESS] 🎉 SERVICE REQUEST SAVED SUCCESSFULLY!`);
        console.log(
          `[SUCCESS] 📊 SAVE RESULT:`,
          JSON.stringify(saveResult, null, 2)
        );

        // Clear collected state
        ServiceRequestService.setCollected(userId, {});
        console.log(`[INFO] Cleared collected state for user ${userId}`);

        return {
          message: `✅ Your service request has been created and booked successfully!`,
        };
      } catch (error) {
        console.error(`[ERROR] Failed to save service request:`, error);
        return {
          message:
            "I apologize, but there was an error saving your service request. Please try again.",
          data: null,
        };
      }
    } else {
      console.log(`\n❌ NOT SAVING TO DATABASE - CONDITIONS NOT MET:`);
      console.log(`[DEBUG] - allDetailsCollected: ${allDetailsCollected}`);
      console.log(`[DEBUG] - isConfirmation: ${isConfirmation}`);
      console.log(
        `[DEBUG] - Both required: ${allDetailsCollected && isConfirmation}`
      );

      if (!allDetailsCollected) {
        console.log(
          `[INFO] 📝 Still collecting data. Missing fields detected.`
        );
      }
      if (!isConfirmation) {
        console.log(
          `[INFO] 🤔 Not a confirmation message. Waiting for user confirmation.`
        );
      }
    }

    return { message: response };
  }

  // Get collected data for a user/session
  static getCollected(userId) {
    if (!llmUserStates[userId]) {
      llmUserStates[userId] = { collected: {} };
    }
    return llmUserStates[userId].collected;
  }

  // Set collected data for a user/session
  static setCollected(userId, collected) {
    if (!llmUserStates[userId]) {
      llmUserStates[userId] = { collected: {} };
    }
    llmUserStates[userId].collected = collected;
  }

  // Helper: Check if all required details are present
  static isAllDetailsCollected(collected) {
    // Ensure collected is an object
    if (!collected || typeof collected !== "object") {
      return false;
    }

    // Check if all required fields are present and have data
    const hasVehicle =
      collected.vehicle && Object.keys(collected.vehicle).length > 0;
    const hasAppointment =
      collected.appointment && Object.keys(collected.appointment).length > 0;
    const hasLocation =
      collected.location && Object.keys(collected.location).length > 0;
    const hasProvider =
      collected.provider && Object.keys(collected.provider).length > 0;
    const hasInstructions =
      collected.instructions && typeof collected.instructions === "object";

    console.log(`[INFO] Detailed field check:`, {
      hasVehicle,
      hasAppointment,
      hasLocation,
      hasProvider,
      hasInstructions,
    });

    // Provider is optional - if not specified, we'll auto-assign one during save
    // Instructions are also optional
    const result = hasVehicle && hasAppointment && hasLocation;

    console.log(`[DEBUG] isAllDetailsCollected result: ${result}`);
    console.log(
      `[DEBUG] Required fields: vehicle=${hasVehicle}, appointment=${hasAppointment}, location=${hasLocation}, provider=${hasProvider} (optional)`
    );

    return result;
  }

  // Helper: Check if the LLM response is a confirmation (improved detection)
  static isConfirmationMessage(response) {
    const confirmationPatterns = [
      /confirm|summary|all set|created|booked|scheduled|done|success/i,
      /would you like to confirm/i,
      /here is a summary/i,
      /service request.*summary/i,
      /would you like to.*book/i,
      /confirm and book/i,
    ];

    const hasConfirmationPattern = confirmationPatterns.some((pattern) =>
      pattern.test(response)
    );

    console.log(`[INFO] Checking confirmation message: "${response}"`);
    console.log(`[INFO] Has confirmation pattern: ${hasConfirmationPattern}`);

    return hasConfirmationPattern;
  }

  // Helper: Check if the user's message is a confirmation
  static isUserConfirmation(userMessage) {
    const userConfirmationPatterns = [
      /^yes\b/i,
      /^yeah\b/i,
      /^yep\b/i,
      /^sure\b/i,
      /^ok\b/i,
      /^okay\b/i,
      /book it/i,
      /confirm/i,
      /go ahead/i,
      /proceed/i,
      /yes.*book/i,
      /yes.*confirm/i,
      /book.*service/i,
      /schedule.*it/i,
    ];

    const isUserConfirming = userConfirmationPatterns.some((pattern) =>
      pattern.test(userMessage.trim())
    );

    console.log(`[INFO] Checking user confirmation: "${userMessage}"`);
    console.log(`[INFO] Is user confirming: ${isUserConfirming}`);

    return isUserConfirming;
  }

  // Extract and update service request data from user message and conversation history
  static async extractAndUpdateData(
    message,
    history,
    currentCollected,
    userId
  ) {
    console.log(`[INFO] Extracting data from message: ${message}`);

    const updated = { ...currentCollected };

    // Extract vehicle information
    if (!updated.vehicle) {
      const vehicleMatch = message.match(/(\w+)\s+(\w+)\s+(\d{4})/i);
      if (vehicleMatch) {
        updated.vehicle = {
          make: vehicleMatch[1],
          model: vehicleMatch[2],
          year: parseInt(vehicleMatch[3]),
          trim: message.includes("Limited") ? "Limited" : null,
          engine: message.match(/(\d+\.?\d*L?\s*V?\d*)/i)?.[1] || null,
          transmission: message.match(/(CVT|Manual|Automatic)/i)?.[1] || null,
        };
        console.log(`[INFO] Extracted vehicle data:`, updated.vehicle);
      }
    }

    // Extract location information
    if (
      !updated.location &&
      (message.includes("garage") ||
        message.includes("address") ||
        message.includes("Building"))
    ) {
      const locationData = {
        address: message,
        city:
          message.match(/([A-Za-z\s]+),\s*[A-Za-z\s]+,\s*\d+/)?.[1] ||
          "Bangalore",
        state: message.match(/,\s*([A-Za-z\s]+),\s*\d+/)?.[1] || "Karnataka",
        zipCode: message.match(/(\d{6})/)?.[1] || null,
        addressType: message.includes("home")
          ? "home"
          : message.includes("work")
          ? "work"
          : "home",
      };
      updated.location = locationData;
      console.log(`[INFO] Extracted location data:`, updated.location);
    }

    // Extract appointment information
    if (
      !updated.appointment &&
      (message.includes("tomorrow") ||
        message.includes("a.m.") ||
        message.includes("p.m.") ||
        message.includes(":"))
    ) {
      const appointmentData = {
        scheduledAt: message.includes("tomorrow")
          ? new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          : new Date().toISOString(),
        timePreference:
          message.match(/(\d{1,2}:\d{2}\s*(a\.m\.|p\.m\.|am|pm))/i)?.[0] ||
          "9:00 a.m.",
      };
      updated.appointment = appointmentData;
      console.log(`[INFO] Extracted appointment data:`, updated.appointment);
    }

    // Extract provider information
    if (
      !updated.provider &&
      (message.toLowerCase().includes("ajay") ||
        message.toLowerCase().includes("assign") ||
        message.toLowerCase().includes("provider"))
    ) {
      if (message.toLowerCase().includes("ajay")) {
        updated.provider = {
          name: "Ajay Koli",
          id: 153,
          email: "<EMAIL>",
          phone: "**********",
        };
        console.log(`[INFO] Extracted provider data:`, updated.provider);
      } else if (
        message.toLowerCase().includes("any available") ||
        message.toLowerCase().includes("available")
      ) {
        updated.provider = {
          name: "Any Available",
          id: null,
          preference: "any_available",
        };
        console.log(`[INFO] Set provider preference to any available`);
      }
    }

    // Extract special instructions
    if (!updated.instructions) {
      if (
        message.toLowerCase().includes("no special") ||
        message.toLowerCase() === "no"
      ) {
        updated.instructions = {
          hasInstructions: false,
          text: "No special instructions",
        };
        console.log(`[INFO] Set instructions to none`);
      } else if (
        message.toLowerCase().includes("handle with care") ||
        message.toLowerCase().includes("careful")
      ) {
        updated.instructions = {
          hasInstructions: true,
          text: message,
        };
        console.log(
          `[INFO] Extracted special instructions:`,
          updated.instructions
        );
      }
    }

    // Save updated data
    ServiceRequestService.setCollected(userId, updated);
    console.log(`[INFO] Saved updated collected data for user ${userId}`);

    return updated;
  }
}
