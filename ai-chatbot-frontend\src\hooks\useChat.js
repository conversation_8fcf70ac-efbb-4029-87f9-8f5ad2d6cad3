import { useState, useRef, useCallback, useEffect } from "react";
import { USER_MESSAGES, ERROR_MESSAGES } from "../utils/messages.js";

export const useChat = (token, sessionId) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      sender: "ai",
      text: USER_MESSAGES.WELCOME,
      timestamp: Date.now(),
    },
  ]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [listening, setListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [micPermissionDenied, setMicPermissionDenied] = useState(false);

  const recognitionRef = useRef(null);
  const speechRef = useRef(null);
  const messagesEndRef = useRef(null);

  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "/api";
  const userId = import.meta.env.VITE_USER_ID;

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Text-to-Speech functionality
  const speak = useCallback((text) => {
    if (!("speechSynthesis" in window)) return;

    // Cancel any existing speech
    window.speechSynthesis.cancel();

    const utterance = new window.SpeechSynthesisUtterance(text);
    utterance.lang = "en-US";
    utterance.rate = 0.9;
    utterance.pitch = 1;

    utterance.onstart = () => {
      setIsSpeaking(true);
      setIsPaused(false);
    };

    utterance.onend = () => {
      setIsSpeaking(false);
      setIsPaused(false);
    };

    utterance.onpause = () => {
      setIsPaused(true);
    };

    utterance.onresume = () => {
      setIsPaused(false);
    };

    speechRef.current = utterance;
    window.speechSynthesis.speak(utterance);
  }, []);

  const pauseSpeech = useCallback(() => {
    if (window.speechSynthesis && isSpeaking) {
      window.speechSynthesis.pause();
      setIsPaused(true);
    }
  }, [isSpeaking]);

  const resumeSpeech = useCallback(() => {
    if (window.speechSynthesis && isPaused) {
      window.speechSynthesis.resume();
      setIsPaused(false);
    }
  }, [isPaused]);

  const stopSpeech = useCallback(() => {
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
      setIsPaused(false);
    }
  }, []);

  // Send message functionality
  const sendMessage = useCallback(
    async (messageText, overrideToken, overrideSessionId) => {
      const trimmedText = messageText.trim();
      if (!trimmedText) return;

      const userMessage = {
        id: Date.now(),
        sender: "user",
        text: trimmedText,
        timestamp: Date.now(),
      };

      setMessages((prev) => [...prev, userMessage]);
      setInput("");
      setIsLoading(true);

      // Add placeholder for AI response
      const loadingMessage = {
        id: Date.now() + 1,
        sender: "ai",
        text: "",
        timestamp: Date.now(),
        isLoading: true,
      };
      setMessages((prev) => [...prev, loadingMessage]);

      try {
        const authToken = overrideToken !== undefined ? overrideToken : token;
        const headers = { "Content-Type": "application/json" };
        if (authToken) {
          headers["Authorization"] = `Bearer ${authToken}`;
        }
        const session =
          overrideSessionId !== undefined ? overrideSessionId : sessionId;
        const body = { message: trimmedText, userId };
        if (session) {
          body.sessionId = session;
        }
        const response = await fetch(`${API_BASE_URL}/chatbot`, {
          method: "POST",
          headers,
          body: JSON.stringify(body),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        let aiData = data;
        console.log("aiData...", aiData);
        // Unwrap if backend wraps in a 'result' field
        if (aiData && aiData.result) {
          aiData = aiData.result;
        }
        // Unwrap if backend wraps in a 'response' field
        if (aiData && aiData.response) {
          aiData = aiData.response;
        }

        // Fix: If aiData is a string, wrap it as { message: aiData, data: null }
        let aiMessageObj;
        if (typeof aiData === "string") {
          aiMessageObj = { message: aiData, data: null };
        } else if (
          aiData &&
          typeof aiData === "object" &&
          aiData.message !== undefined
        ) {
          aiMessageObj = {
            message: aiData.message,
            data: aiData.data === undefined ? null : aiData.data,
          };
        } else {
          aiMessageObj = { message: "", data: null };
        }

        setMessages((prev) => {
          const updated = [...prev];
          updated[updated.length - 1] = {
            id: Date.now() + 2,
            sender: "ai",
            data: aiMessageObj, // always pass as {message, data}
            text: aiMessageObj.message || "",
            timestamp: Date.now(),
            isLoading: false,
          };
          return updated;
        });

        // Speak the AI response (always use aiMessageObj.message)
        let textToSpeak = aiMessageObj.message || "";
        // If there's provider data, enhance the speech
        if (aiMessageObj.data) {
          if (
            Array.isArray(aiMessageObj.data) &&
            aiMessageObj.data.length > 0
          ) {
            // Provider list
            const providers = aiMessageObj.data;
            const maxProvidersToSpeak = 5;
            const providersToSpeak = providers.slice(0, maxProvidersToSpeak);
            const remainingCount = providers.length - maxProvidersToSpeak;

            textToSpeak += ` Here are the ${providers.length} providers with their details: `;

            providersToSpeak.forEach((provider, index) => {
              textToSpeak += `${index + 1}. ${provider.name}`;
              if (provider.email) textToSpeak += `, email: ${provider.email}`;
              if (provider.phone) textToSpeak += `, phone: ${provider.phone}`;
              if (provider.services && provider.services.length > 0) {
                textToSpeak += `. Services: `;
                provider.services.forEach((service, serviceIndex) => {
                  textToSpeak += `${service.name}`;
                  if (service.description)
                    textToSpeak += ` (${service.description})`;
                  if (service.price) textToSpeak += ` - ${service.price}`;
                  if (serviceIndex < provider.services.length - 1)
                    textToSpeak += ", ";
                });
              }
              if (index < providersToSpeak.length - 1) textToSpeak += ". ";
            });

            if (remainingCount > 0) {
              textToSpeak += `. And ${remainingCount} more providers. You can ask me about any specific provider for more details.`;
            } else {
              textToSpeak += `. You can ask me about any specific provider for more details.`;
            }
          } else if (aiMessageObj.data && !Array.isArray(aiMessageObj.data)) {
            // Single provider detail
            const provider = aiMessageObj.data;
            textToSpeak += ` Here are the complete details for ${provider.name}`;
            if (provider.email)
              textToSpeak += `. Their email is ${provider.email}`;
            if (provider.phone)
              textToSpeak += `. Their phone number is ${provider.phone}`;
            if (provider.services && provider.services.length > 0) {
              textToSpeak += `. They offer the following services: `;
              provider.services.forEach((service, index) => {
                textToSpeak += `${index + 1}. ${service.name}`;
                if (service.description)
                  textToSpeak += ` - ${service.description}`;
                if (service.price) textToSpeak += ` at ${service.price}`;
                if (index < provider.services.length - 1) textToSpeak += ". ";
              });
            } else {
              textToSpeak += `. No services are currently listed for this provider.`;
            }
          }
        }
        speak(textToSpeak);
      } catch (error) {
        console.error("Chat error:", error);
        setMessages((prev) => {
          const updated = [...prev];
          updated[updated.length - 1] = {
            id: Date.now() + 3,
            sender: "ai",
            text: USER_MESSAGES.SERVER_ERROR,
            timestamp: Date.now(),
            isLoading: false,
            isError: true,
          };
          return updated;
        });
      } finally {
        setIsLoading(false);
      }
    },
    [speak, token, sessionId]
  );

  // Check microphone permission status
  const checkMicrophonePermission = async () => {
    try {
      const permission = await navigator.permissions.query({
        name: "microphone",
      });
      setMicPermissionDenied(permission.state === "denied");
      return permission.state;
    } catch (error) {
      // Permissions API not supported, fallback to direct check
      setMicPermissionDenied(false);
      return "unknown";
    }
  };

  // Check permission status on component mount
  useEffect(() => {
    checkMicrophonePermission();
  }, []);

  // Speech recognition functionality
  const startListening = useCallback(async () => {
    // Check if speech recognition is supported
    if (
      !("webkitSpeechRecognition" in window || "SpeechRecognition" in window)
    ) {
      alert(ERROR_MESSAGES.SPEECH_RECOGNITION_NOT_SUPPORTED);
      return;
    }

    // Check if we're on HTTPS or localhost (required for speech recognition)
    if (
      location.protocol !== "https:" &&
      location.hostname !== "localhost" &&
      location.hostname !== "127.0.0.1"
    ) {
      alert(ERROR_MESSAGES.SPEECH_RECOGNITION_HTTPS_REQUIRED);
      return;
    }

    // Check current permission status
    const permissionStatus = await checkMicrophonePermission();

    if (permissionStatus === "denied") {
      const shouldRetry = confirm(
        "Microphone access was previously denied.\n\n" +
          "To enable voice input:\n" +
          "1. Click the microphone icon in your browser's address bar\n" +
          "2. Select 'Always allow' or 'Allow'\n" +
          "3. Refresh the page\n\n" +
          "Would you like to try again anyway?"
      );

      if (!shouldRetry) {
        return;
      }
    }

    try {
      // Try to get microphone permission
      let stream;
      try {
        stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        stream.getTracks().forEach((track) => track.stop()); // Stop the stream, we just needed permission
      } catch (permissionError) {
        // If getUserMedia fails, try speech recognition directly (some browsers handle it differently)
        // Direct getUserMedia failed, trying speech recognition directly...
      }

      const SpeechRecognition =
        window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();

      recognition.lang = "en-US";
      recognition.interimResults = false;
      recognition.maxAlternatives = 1;
      recognition.continuous = false;

      recognition.onstart = () => {
        // Speech recognition started
        setListening(true);
      };

      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        // Speech recognition result: transcript
        setInput(transcript);
        sendMessage(transcript);
      };

      recognition.onerror = (event) => {
        console.error("Speech recognition error:", event.error);
        setListening(false);

        // Handle specific error types
        switch (event.error) {
          case "not-allowed":
            alert(ERROR_MESSAGES.SPEECH_RECOGNITION_PERMISSION_DENIED);
            break;
          case "no-speech":
            alert(ERROR_MESSAGES.SPEECH_RECOGNITION_NO_SPEECH);
            break;
          case "audio-capture":
            alert(ERROR_MESSAGES.SPEECH_RECOGNITION_NO_MICROPHONE);
            break;
          case "network":
            alert(ERROR_MESSAGES.SPEECH_RECOGNITION_NETWORK_ERROR);
            break;
          case "service-not-allowed":
            alert(ERROR_MESSAGES.SPEECH_RECOGNITION_SERVICE_ERROR);
            break;
          default:
            alert(
              `Speech recognition error: ${event.error}\n\nPlease try typing your message instead.`
            );
        }
      };

      recognition.onend = () => {
        // Speech recognition ended
        setListening(false);
      };

      recognitionRef.current = recognition;
      recognition.start();
    } catch (error) {
      console.error("Microphone permission error:", error);
      setListening(false);

      if (
        error.name === "NotAllowedError" ||
        error.name === "PermissionDeniedError"
      ) {
        alert(
          "Microphone access was denied.\n\n" +
            "To enable voice input:\n" +
            "1. Refresh this page\n" +
            "2. When prompted, click 'Allow' for microphone access\n" +
            "3. Or manually enable in browser settings:\n" +
            "   - Chrome: Click the lock icon → Microphone → Allow\n" +
            "   - Firefox: Click the shield icon → Permissions\n" +
            "   - Safari: Safari menu → Settings → Websites → Microphone"
        );
      } else if (error.name === "NotFoundError") {
        alert(
          "No microphone was found. Please ensure that a microphone is connected and try again."
        );
      } else if (error.name === "AbortError") {
        alert("Microphone access was interrupted. Please try again.");
      } else {
        alert(
          "Unable to access microphone.\n\n" +
            "Please check:\n" +
            "1. Your microphone is working\n" +
            "2. Browser permissions are enabled\n" +
            "3. No other applications are using the microphone\n\n" +
            "You can always type your message instead."
        );
      }
    }
  }, [sendMessage]);

  const stopListening = useCallback(() => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      setListening(false);
    }
  }, []);

  // Cleanup function
  useEffect(() => {
    return () => {
      // Clean up speech recognition
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      // Clean up speech synthesis
      if (window.speechSynthesis) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  return {
    messages,
    input,
    setInput,
    isLoading,
    listening,
    isSpeaking,
    isPaused,
    micPermissionDenied,
    messagesEndRef,
    sendMessage,
    startListening,
    stopListening,
    pauseSpeech,
    resumeSpeech,
    stopSpeech,
  };
};
