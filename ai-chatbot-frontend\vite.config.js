import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react()],
  css: {
    modules: {
      localsConvention: "camelCase",
    },
  },
  server: {
    port: 3030,
    strictPort: false,
    hmr: {
      port: 3030,
    },
    proxy: {
      "/conversational-service-request": "http://localhost:3000",
      "/api": "http://localhost:3001",
    },
  },
});
