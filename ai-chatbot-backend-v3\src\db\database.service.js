import { DataSource } from "typeorm";
import { DATABASE_URL } from "../config/env.js";

export class DatabaseService {
  constructor() {
    this.dataSource = null;
  }

  async initialize() {
    if (!DATABASE_URL) {
      throw new Error("Database URL is missing");
    }
    if (this.dataSource) return;

    console.log("[INFO] Initializing database connection");

    this.dataSource = new DataSource({
      type: "postgres",
      url: DATABASE_URL,
      synchronize: false,
      logging: false,
      entities: [], // Add your entities here if using ORM
      ssl: DATABASE_URL.includes("localhost")
        ? false
        : { rejectUnauthorized: false },
    });

    console.log("[INFO] Connecting to PostgreSQL database");
    await this.dataSource.initialize();
    console.log("[SUCCESS] Database connected successfully");
    console.log("[SUCCESS] Database initialized successfully");
  }

  getDataSource() {
    if (!this.dataSource) {
      throw new Error("Database not initialized");
    }
    return this.dataSource;
  }
}

export const databaseService = new DatabaseService();
