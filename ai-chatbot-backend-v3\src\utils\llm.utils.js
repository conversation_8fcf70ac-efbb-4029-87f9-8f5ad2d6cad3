export class LLMUtils {
  /**
   * Recursively search for token usage in response object
   * @param {object} obj - Object to search
   * @param {string} path - Current path for debugging
   * @returns {object|null} - Token usage object or null
   */
  static findTokenUsage(obj, path = "") {
    if (!obj || typeof obj !== "object") return null;

    // Check if this object has token usage properties (camelCase format)
    if (
      obj.promptTokens !== undefined ||
      obj.completionTokens !== undefined ||
      obj.totalTokens !== undefined
    ) {
      return {
        prompt_tokens: obj.promptTokens || 0,
        response_tokens: obj.completionTokens || 0,
        total_tokens: obj.totalTokens || 0,
      };
    }

    // Check if this object has token usage properties (snake_case format)
    if (
      obj.prompt_tokens !== undefined ||
      obj.response_tokens !== undefined ||
      obj.total_tokens !== undefined
    ) {
      return {
        prompt_tokens: obj.prompt_tokens || 0,
        response_tokens: obj.response_tokens || 0,
        total_tokens: obj.total_tokens || 0,
      };
    }

    // Check for Google Generative AI format
    if (
      obj.promptTokenCount !== undefined ||
      obj.candidatesTokenCount !== undefined ||
      obj.totalTokenCount !== undefined
    ) {
      return {
        prompt_tokens: obj.promptTokenCount || 0,
        response_tokens: obj.candidatesTokenCount || 0,
        total_tokens: obj.totalTokenCount || 0,
      };
    }

    // Recursively search through all properties
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === "object" && value !== null) {
        const result = this.findTokenUsage(value, `${path}.${key}`);
        if (result) return result;
      }
    }

    return null;
  }

  /**
   * Log token usage from LLM response
   * @param {object} response - LLM response object
   * @param {string} operation - Operation name for logging
   */
  static logTokenUsage(response, operation) {
    try {
      // Check for different token usage formats
      let usage = null;

      // Format 1: response_metadata.token_usage (OpenAI style)
      if (
        response.response_metadata &&
        response.response_metadata.token_usage
      ) {
        usage = response.response_metadata.token_usage;
      }
      // Format 1.5: response_metadata with camelCase token properties
      else if (
        response.response_metadata &&
        (response.response_metadata.promptTokens !== undefined ||
          response.response_metadata.completionTokens !== undefined ||
          response.response_metadata.totalTokens !== undefined)
      ) {
        usage = {
          prompt_tokens: response.response_metadata.promptTokens || 0,
          response_tokens: response.response_metadata.completionTokens || 0,
          total_tokens: response.response_metadata.totalTokens || 0,
        };
      }
      // Format 2: Direct token_usage property
      else if (response.token_usage) {
        usage = response.token_usage;
      }
      // Format 3: Google Generative AI format
      else if (response.usageMetadata) {
        usage = {
          prompt_tokens: response.usageMetadata.promptTokenCount,
          response_tokens: response.usageMetadata.candidatesTokenCount,
          total_tokens: response.usageMetadata.totalTokenCount,
        };
      }
      // Format 4: Check for any token-related properties
      else if (
        response.promptTokenCount ||
        response.candidatesTokenCount ||
        response.totalTokenCount
      ) {
        usage = {
          prompt_tokens: response.promptTokenCount,
          response_tokens: response.candidatesTokenCount,
          total_tokens: response.totalTokenCount,
        };
      }
      // Format 5: Deep nested response_metadata structure
      else if (response.response_metadata) {
        // Log the full response_metadata structure for debugging
        // console.log(
        //   `[DEBUG] ${operation} - Full response_metadata:`,
        //   JSON.stringify(response.response_metadata, null, 2)
        // );

        // Try to find token usage in nested structure
        const metadata = response.response_metadata;
        if (metadata.token_usage) {
          usage = metadata.token_usage;
        } else if (metadata.usage) {
          usage = metadata.usage;
        } else if (metadata.tokens) {
          usage = metadata.tokens;
        }
      }

      // Try recursive search as a fallback
      if (!usage) {
        usage = this.findTokenUsage(response);
      }

      if (usage) {
        console.log(`[INFO] ${operation} Token Usage:`, {
          inputTokens: usage.prompt_tokens || 0,
          outputTokens: usage.response_tokens || 0,
          totalTokens: usage.total_tokens || 0,
          operation: operation,
        });
      } else {
        // Log the response structure for debugging
        console.log(`[INFO] ${operation} completed - Response structure:`, {
          hasResponseMetadata: !!response.response_metadata,
          hasTokenUsage: !!response.token_usage,
          hasUsageMetadata: !!response.usageMetadata,
          keys: Object.keys(response).filter((key) =>
            key.toLowerCase().includes("token")
          ),
          operation: operation,
        });
      }
    } catch (error) {
      console.warn(
        `[WARNING] Could not log token usage for ${operation}:`,
        error
      );
    }
  }

  /**
   * Handle LLM response with consistent error handling
   * @param {object} result - LLM result object
   * @param {string} operation - Operation name
   * @param {boolean} expectJson - Whether to expect JSON response
   * @returns {object} - Processed result
   */
  static handleLLMResponse(result, operation, expectJson = false) {
    try {
      // Log token usage
      this.logTokenUsage(result, operation);

      if (expectJson) {
        // For JSON responses, return the parsed content
        return {
          success: true,
          content: result.content,
          operation: operation,
        };
      } else {
        // For text responses, return the content directly
        return {
          success: true,
          content: result.content,
          operation: operation,
        };
      }
    } catch (error) {
      console.error(`[ERROR] ${operation}: Response handling failed`, error);
      return {
        success: false,
        error: error.message,
        operation: operation,
      };
    }
  }

  /**
   * Create a retry mechanism for LLM calls
   * @param {Function} llmFunction - Function to retry
   * @param {number} maxRetries - Maximum number of retries
   * @param {number} delay - Delay between retries in ms
   * @returns {Promise} - Result of the LLM function
   */
  static async retryLLMCall(llmFunction, maxRetries = 3, delay = 1000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await llmFunction();
      } catch (error) {
        console.warn(
          `[WARNING] LLM call attempt ${attempt} failed:`,
          error.message
        );

        if (attempt === maxRetries) {
          throw error;
        }

        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, delay * attempt));
      }
    }
  }

  /**
   * Validate LLM response format
   * @param {object} response - LLM response
   * @param {string} expectedType - Expected response type ('json' or 'text')
   * @returns {boolean} - Whether response is valid
   */
  static validateResponse(response, expectedType = "text") {
    if (!response || typeof response.content !== "string") {
      return false;
    }

    if (expectedType === "json") {
      // Try to parse as JSON
      try {
        JSON.parse(response.content);
        return true;
      } catch (error) {
        return false;
      }
    }

    return true;
  }
}
