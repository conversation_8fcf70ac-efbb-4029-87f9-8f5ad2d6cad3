import express from "express";
import { ServiceRequestToolController } from "../controllers/service-request-tool.controller.js";

const router = express.Router();

// Create service request
router.post("/create", ServiceRequestToolController.createServiceRequest);

// Get user's vehicles
router.get("/vehicles/:userId", ServiceRequestToolController.getUserVehicles);

// Get user's addresses
router.get("/addresses/:userId", ServiceRequestToolController.getUserAddresses);

// Find similar vehicles
router.post("/vehicles/similar", ServiceRequestToolController.findSimilarVehicles);

// Find similar addresses
router.post("/addresses/similar", ServiceRequestToolController.findSimilarAddresses);

// Validate service request data
router.post("/validate", ServiceRequestToolController.validateServiceRequestData);

export default router;
